#!/bin/bash

# Turkish Analysis Library Verification Script for Solr 9.8.1
# This script tests that custom Turkish analysis libraries are properly loaded and functional

set -e

SOLR_URL="http://localhost:8981"
COLLECTION="market"
LOG_FILE="turkish_analysis_test_$(date +%Y%m%d_%H%M%S).log"

echo "=== Turkish Analysis Library Verification Test ===" | tee $LOG_FILE
echo "Started at: $(date)" | tee -a $LOG_FILE
echo "" | tee -a $LOG_FILE

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "PASS")
            echo -e "${GREEN}✅ PASS${NC}: $message" | tee -a $LOG_FILE
            ;;
        "FAIL")
            echo -e "${RED}❌ FAIL${NC}: $message" | tee -a $LOG_FILE
            ;;
        "WARN")
            echo -e "${YELLOW}⚠️  WARN${NC}: $message" | tee -a $LOG_FILE
            ;;
        "INFO")
            echo -e "ℹ️  INFO: $message" | tee -a $LOG_FILE
            ;;
    esac
}

# Function to test HTTP endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    print_status "INFO" "Testing: $description"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/solr_response.json "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        print_status "PASS" "$description - HTTP $response"
        return 0
    else
        print_status "FAIL" "$description - HTTP $response (expected $expected_status)"
        return 1
    fi
}

# Test 1: Verify Solr is running
print_status "INFO" "=== Test 1: Basic Solr Connectivity ==="
test_endpoint "$SOLR_URL/solr/admin/ping" "Solr Admin Ping"

# Test 2: Check custom library loading
print_status "INFO" "=== Test 2: Custom Library Loading Verification ==="

# Test if Turkish analysis classes are available
turkish_analysis_query="$SOLR_URL/solr/$COLLECTION/analysis/field?analysis.fieldname=product_name&analysis.fieldvalue=çikolata&wt=json"
response=$(curl -s "$turkish_analysis_query" 2>/dev/null)
http_code=$(curl -s -w "%{http_code}" -o /dev/null "$turkish_analysis_query" 2>/dev/null)

if [ "$http_code" = "200" ]; then
    print_status "PASS" "Analysis endpoint accessible"
    
    # Check if Turkish-specific analysis is working
    if echo "$response" | grep -q "analysis"; then
        print_status "PASS" "Analysis response contains expected structure"
    else
        print_status "WARN" "Analysis response may not contain expected Turkish analysis"
    fi
else
    print_status "FAIL" "Analysis endpoint not accessible - HTTP $http_code"
fi

# Test 3: Test Turkish text analysis
print_status "INFO" "=== Test 3: Turkish Text Analysis ==="

# Test Turkish characters and morphology
turkish_test_words=("çikolata" "kahve" "şeker" "süt" "ekmek")

for word in "${turkish_test_words[@]}"; do
    print_status "INFO" "Testing Turkish word: $word"
    
    analysis_url="$SOLR_URL/solr/$COLLECTION/analysis/field?analysis.fieldname=product_name&analysis.fieldvalue=$word&wt=json"
    response=$(curl -s "$analysis_url" 2>/dev/null)
    http_code=$(curl -s -w "%{http_code}" -o /dev/null "$analysis_url" 2>/dev/null)
    
    if [ "$http_code" = "200" ]; then
        # Check if the response contains analysis results
        if echo "$response" | jq -e '.analysis' > /dev/null 2>&1; then
            print_status "PASS" "Turkish analysis working for: $word"
        else
            print_status "WARN" "Analysis response format unexpected for: $word"
        fi
    else
        print_status "FAIL" "Analysis failed for Turkish word: $word (HTTP $http_code)"
    fi
done

# Test 4: Test Zemberek morphological analysis
print_status "INFO" "=== Test 4: Zemberek Morphological Analysis ==="

# Test if Zemberek libraries are working by analyzing complex Turkish words
zemberek_test_words=("kitaplarımızdan" "çalışabilecekler" "öğrencilerimiz")

for word in "${zemberek_test_words[@]}"; do
    print_status "INFO" "Testing Zemberek analysis for: $word"
    
    analysis_url="$SOLR_URL/solr/$COLLECTION/analysis/field?analysis.fieldname=product_name&analysis.fieldvalue=$word&wt=json"
    response=$(curl -s "$analysis_url" 2>/dev/null)
    http_code=$(curl -s -w "%{http_code}" -o /dev/null "$analysis_url" 2>/dev/null)
    
    if [ "$http_code" = "200" ]; then
        print_status "PASS" "Zemberek analysis accessible for: $word"
        
        # Log the analysis result for manual inspection
        echo "Analysis result for $word:" >> $LOG_FILE
        echo "$response" | jq '.analysis' >> $LOG_FILE 2>/dev/null || echo "Could not parse JSON" >> $LOG_FILE
        echo "" >> $LOG_FILE
    else
        print_status "FAIL" "Zemberek analysis failed for: $word (HTTP $http_code)"
    fi
done

# Test 5: Test search functionality with Turkish text
print_status "INFO" "=== Test 5: Turkish Search Functionality ==="

# Test searching for Turkish products
search_query="$SOLR_URL/solr/$COLLECTION/select?q=product_name:çikolata&rows=5&wt=json"
response=$(curl -s "$search_query" 2>/dev/null)
http_code=$(curl -s -w "%{http_code}" -o /dev/null "$search_query" 2>/dev/null)

if [ "$http_code" = "200" ]; then
    num_found=$(echo "$response" | jq '.response.numFound' 2>/dev/null || echo "0")
    print_status "PASS" "Turkish search query executed successfully"
    print_status "INFO" "Found $num_found documents for 'çikolata'"
else
    print_status "FAIL" "Turkish search query failed - HTTP $http_code"
fi

# Test 6: Verify custom JAR files are loaded
print_status "INFO" "=== Test 6: Custom JAR File Verification ==="

# Check Solr system info for loaded libraries
system_info_url="$SOLR_URL/solr/admin/info/system?wt=json"
response=$(curl -s "$system_info_url" 2>/dev/null)
http_code=$(curl -s -w "%{http_code}" -o /dev/null "$system_info_url" 2>/dev/null)

if [ "$http_code" = "200" ]; then
    print_status "PASS" "System info accessible"
    
    # Check classpath for Turkish analysis libraries
    classpath=$(echo "$response" | jq -r '.system.classpath' 2>/dev/null || echo "")
    
    if echo "$classpath" | grep -q "TurkishAnalysis"; then
        print_status "PASS" "TurkishAnalysis library found in classpath"
    else
        print_status "WARN" "TurkishAnalysis library not explicitly found in classpath"
    fi
    
    if echo "$classpath" | grep -q "zemberek"; then
        print_status "PASS" "Zemberek libraries found in classpath"
    else
        print_status "WARN" "Zemberek libraries not explicitly found in classpath"
    fi
else
    print_status "FAIL" "System info not accessible - HTTP $http_code"
fi

# Test 7: Test field type configuration
print_status "INFO" "=== Test 7: Field Type Configuration ==="

# Check if Turkish field types are properly configured
schema_url="$SOLR_URL/solr/$COLLECTION/schema/fieldtypes?wt=json"
response=$(curl -s "$schema_url" 2>/dev/null)
http_code=$(curl -s -w "%{http_code}" -o /dev/null "$schema_url" 2>/dev/null)

if [ "$http_code" = "200" ]; then
    print_status "PASS" "Schema field types accessible"
    
    # Check for Turkish-specific field types
    if echo "$response" | jq -e '.fieldTypes[] | select(.name | contains("turkish") or contains("Turkish"))' > /dev/null 2>&1; then
        print_status "PASS" "Turkish field types found in schema"
    else
        print_status "INFO" "No explicit Turkish field types found (may use standard text types)"
    fi
else
    print_status "FAIL" "Schema field types not accessible - HTTP $http_code"
fi

# Test 8: Performance test with Turkish content
print_status "INFO" "=== Test 8: Performance Test with Turkish Content ==="

# Run multiple Turkish queries to test performance
turkish_queries=("çikolata" "kahve" "süt" "ekmek" "şeker")
total_time=0
successful_queries=0

for query in "${turkish_queries[@]}"; do
    start_time=$(date +%s%3N)
    response=$(curl -s "$SOLR_URL/solr/$COLLECTION/select?q=product_name:$query&rows=10" 2>/dev/null)
    end_time=$(date +%s%3N)
    
    query_time=$((end_time - start_time))
    total_time=$((total_time + query_time))
    
    if echo "$response" | jq -e '.response' > /dev/null 2>&1; then
        successful_queries=$((successful_queries + 1))
        print_status "INFO" "Query '$query': ${query_time}ms"
    else
        print_status "WARN" "Query '$query' failed or returned unexpected format"
    fi
done

if [ $successful_queries -eq ${#turkish_queries[@]} ]; then
    avg_time=$((total_time / ${#turkish_queries[@]}))
    print_status "PASS" "Turkish query performance test - ${successful_queries}/${#turkish_queries[@]} queries successful, avg: ${avg_time}ms"
else
    print_status "WARN" "Turkish query performance test - Only $successful_queries/${#turkish_queries[@]} queries successful"
fi

# Summary
echo "" | tee -a $LOG_FILE
echo "=== Test Summary ===" | tee -a $LOG_FILE
echo "Completed at: $(date)" | tee -a $LOG_FILE
echo "Log file: $LOG_FILE" | tee -a $LOG_FILE
echo "" | tee -a $LOG_FILE

# Count results
pass_count=$(grep -c "✅ PASS" $LOG_FILE || echo "0")
fail_count=$(grep -c "❌ FAIL" $LOG_FILE || echo "0")
warn_count=$(grep -c "⚠️  WARN" $LOG_FILE || echo "0")

echo "Results:" | tee -a $LOG_FILE
echo "  PASS: $pass_count" | tee -a $LOG_FILE
echo "  FAIL: $fail_count" | tee -a $LOG_FILE
echo "  WARN: $warn_count" | tee -a $LOG_FILE

if [ $fail_count -eq 0 ]; then
    print_status "PASS" "Turkish Analysis library verification completed successfully!"
    echo "" | tee -a $LOG_FILE
    echo "✅ All Turkish analysis libraries are properly loaded and functional in Solr 9.8.1" | tee -a $LOG_FILE
    exit 0
else
    print_status "FAIL" "Turkish Analysis library verification failed with $fail_count failures"
    echo "" | tee -a $LOG_FILE
    echo "❌ Some Turkish analysis functionality may not be working correctly" | tee -a $LOG_FILE
    echo "Check the log file for details: $LOG_FILE" | tee -a $LOG_FILE
    exit 1
fi
