# Solr Faceting Test Plan

## Testing Strategy

To verify that faceting works correctly, we need to test the following scenarios:

### 1. **Basic Faceting Test**
- Query without any filters
- Verify that `offer_market_facet` returns all available markets
- Verify that facet counts are reasonable and non-zero

### 2. **Depot Filter Impact on Market Facets**
- Apply depot filter (e.g., only "migros-1234", "a101-5678")
- Verify that `offer_market_facet` only shows markets that have offers in those depots
- This is the key test - facets should be contextually filtered

### 3. **Market Filter with excludeTags**
- Apply market filter (e.g., "migros")
- Verify that `offer_market_facet` still shows all markets (due to excludeTags)
- Verify that other aspects of the query are filtered by the market selection

### 4. **Multiple Filter Combination**
- Apply depot filter + category filter + price filter
- Verify that market facets only show markets available for products matching all filters
- Verify that facet counts reflect the filtered result set

### 5. **Parameter Substitution Verification**
- Inspect generated Solr query
- Verify that JSON facets use parameter substitution ($parentFilter, $childFilter)
- Verify that parameters are properly set in the query

## Test Implementation Approach

1. **Unit Tests**: Mock Solr client and verify query structure
2. **Integration Tests**: Use embedded Solr or test against real Solr instance
3. **Manual Testing**: Direct Solr queries to verify behavior
4. **Query Inspection**: Log and analyze generated Solr queries

## Success Criteria

- Facets are contextually filtered based on applied filters
- Parameter substitution works correctly
- Filter consistency across query, facets, and projection
- Clean, maintainable code structure
- No performance degradation

## Test Data Requirements

For effective testing, we need:
- Products with multiple depot offers
- Products spanning multiple markets
- Products in different categories and price ranges
- Sufficient data volume to verify facet behavior

## Failure Scenarios to Test

1. **Empty Facet Results**: When filters are too restrictive
2. **Incorrect Facet Counts**: When parameter substitution fails
3. **Missing Context**: When facets don't respect applied filters
4. **Performance Issues**: When queries become too complex
