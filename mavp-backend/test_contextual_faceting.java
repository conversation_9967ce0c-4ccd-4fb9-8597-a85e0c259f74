import java.lang.reflect.Method;
import java.util.Arrays;

import org.apache.solr.client.solrj.SolrQuery;

import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;
import tr.gov.tubitak.mavp.data.solr.repository.ProductRepositorySolrJImpl;

import static org.mockito.Mockito.*;

/**
 * Manual test to verify contextual faceting implementation
 */
public class test_contextual_faceting {
    
    public static void main(String[] args) {
        try {
            System.out.println("=== Testing Enhanced Contextual Faceting ===");
            
            // Create mock config
            SolrBeanConfig mockConfig = mock(SolrBeanConfig.class);
            when(mockConfig.getSolrClient()).thenReturn(null);
            
            // Create repository instance
            ProductRepositorySolrJImpl repository = new ProductRepositorySolrJImpl(mockConfig);
            
            // Test scenario: User applies category filter + depot filter
            System.out.println("\n=== Test Scenario: Category + Depot Filters ===");
            testCategoryAndDepotFilters(repository);
            
            // Test scenario: Multiple user filters
            System.out.println("\n=== Test Scenario: Multiple User Filters ===");
            testMultipleUserFilters(repository);
            
            // Test scenario: Market filter with excludeTags
            System.out.println("\n=== Test Scenario: Market Filter with excludeTags ===");
            testMarketFilterWithExcludeTags(repository);
            
            // Test scenario: No user filters (baseline)
            System.out.println("\n=== Test Scenario: No User Filters (Baseline) ===");
            testNoUserFilters(repository);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void testCategoryAndDepotFilters(ProductRepositorySolrJImpl repository) throws Exception {
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setOtherFilters("main_category", Arrays.asList("Çikolata"));
        searchDto.setDepots(Arrays.asList("migros-1234", "a101-5678"));
        
        SolrQuery query = buildQuery(repository, searchDto);
        
        String parentFilter = query.get("parentFilter");
        String basicParentFilter = query.get("basicParentFilter");
        String childFilter = query.get("childFilter");
        
        System.out.println("Basic Parent Filter: " + basicParentFilter);
        System.out.println("Enhanced Parent Filter: " + parentFilter);
        System.out.println("Child Filter: " + childFilter);
        
        // Verify contextual filtering
        boolean isContextual = parentFilter != null && 
                              parentFilter.contains("*:* -_nest_path_:*") &&
                              parentFilter.contains("main_category:\"Çikolata\"");
        
        System.out.println("✓ Contextual filtering working: " + isContextual);
        System.out.println("Expected: Market facets will only show markets that have chocolate products in specified depots");
        
        if (!isContextual) {
            System.out.println("❌ ISSUE: Parent filter doesn't include category filter!");
        }
    }
    
    private static void testMultipleUserFilters(ProductRepositorySolrJImpl repository) throws Exception {
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setOtherFilters("main_category", Arrays.asList("Çikolata"));
        searchDto.setOtherFilters("brand", Arrays.asList("Ülker"));
        searchDto.setOtherFilters("refined_quantity_unit", Arrays.asList("gr"));
        searchDto.setDepots(Arrays.asList("migros-1234"));
        
        SolrQuery query = buildQuery(repository, searchDto);
        
        String parentFilter = query.get("parentFilter");
        
        System.out.println("Enhanced Parent Filter: " + parentFilter);
        
        boolean includesCategory = parentFilter.contains("main_category:\"Çikolata\"");
        boolean includesBrand = parentFilter.contains("brand:\"Ülker\"");
        boolean includesUnit = parentFilter.contains("refined_quantity_unit:\"gr\"");
        
        System.out.println("✓ Includes category filter: " + includesCategory);
        System.out.println("✓ Includes brand filter: " + includesBrand);
        System.out.println("✓ Includes unit filter: " + includesUnit);
        
        boolean allFiltersIncluded = includesCategory && includesBrand && includesUnit;
        System.out.println("✓ All user filters included: " + allFiltersIncluded);
        System.out.println("Expected: Market facets will only show markets that have Ülker chocolate products (in grams) in migros-1234");
    }
    
    private static void testMarketFilterWithExcludeTags(ProductRepositorySolrJImpl repository) throws Exception {
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setOtherFilters("offer_market", Arrays.asList("migros"));
        searchDto.setOtherFilters("main_category", Arrays.asList("Çikolata"));
        
        SolrQuery query = buildQuery(repository, searchDto);
        
        String parentFilter = query.get("parentFilter");
        String[] filterQueries = query.getFilterQueries();
        
        System.out.println("Enhanced Parent Filter: " + parentFilter);
        System.out.println("Filter Queries:");
        if (filterQueries != null) {
            for (int i = 0; i < filterQueries.length; i++) {
                System.out.println("  " + (i+1) + ": " + filterQueries[i]);
            }
        }
        
        // Verify market filter is tagged but not included in contextual parent filter
        boolean hasTaggedMarketFilter = false;
        if (filterQueries != null) {
            for (String fq : filterQueries) {
                if (fq.contains("OFFER_MARKET_FILTER_TAG") && fq.contains("offer_market:\"migros\"")) {
                    hasTaggedMarketFilter = true;
                    break;
                }
            }
        }
        
        boolean categoryInParentFilter = parentFilter.contains("main_category:\"Çikolata\"");
        boolean marketNotInParentFilter = !parentFilter.contains("offer_market");
        
        System.out.println("✓ Market filter is tagged: " + hasTaggedMarketFilter);
        System.out.println("✓ Category filter in parent filter: " + categoryInParentFilter);
        System.out.println("✓ Market filter NOT in parent filter: " + marketNotInParentFilter);
        
        boolean excludeTagsWorking = hasTaggedMarketFilter && categoryInParentFilter && marketNotInParentFilter;
        System.out.println("✓ ExcludeTags mechanism working: " + excludeTagsWorking);
        System.out.println("Expected: Market facets will show ALL markets (due to excludeTags) but only for chocolate products");
    }
    
    private static void testNoUserFilters(ProductRepositorySolrJImpl repository) throws Exception {
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setDepots(Arrays.asList("migros-1234", "a101-5678"));
        
        SolrQuery query = buildQuery(repository, searchDto);
        
        String parentFilter = query.get("parentFilter");
        String basicParentFilter = query.get("basicParentFilter");
        
        System.out.println("Basic Parent Filter: " + basicParentFilter);
        System.out.println("Enhanced Parent Filter: " + parentFilter);
        
        boolean isBasicFilter = "*:* -_nest_path_:*".equals(parentFilter);
        System.out.println("✓ Parent filter is basic (no user filters): " + isBasicFilter);
        System.out.println("Expected: Market facets will show all markets available in specified depots");
    }
    
    private static SolrQuery buildQuery(ProductRepositorySolrJImpl repository, BaseSearchDto searchDto) throws Exception {
        SolrQuery query = new SolrQuery();
        
        // Call the private methods in the correct order (matching the actual implementation)
        Method setupBasicMethod = ProductRepositorySolrJImpl.class.getDeclaredMethod("setupBasicParentChildFilterParameters", SolrQuery.class, java.util.List.class);
        setupBasicMethod.setAccessible(true);
        setupBasicMethod.invoke(repository, query, searchDto.getDepots());
        
        Method addFiltersMethod = ProductRepositorySolrJImpl.class.getDeclaredMethod("addFilters", tr.gov.tubitak.mavp.data.api.BaseSearchDto.class, SolrQuery.class);
        addFiltersMethod.setAccessible(true);
        addFiltersMethod.invoke(repository, searchDto, query);
        
        Method setupContextualMethod = ProductRepositorySolrJImpl.class.getDeclaredMethod("setupContextualFilterParameters", SolrQuery.class, tr.gov.tubitak.mavp.data.api.BaseSearchDto.class);
        setupContextualMethod.setAccessible(true);
        setupContextualMethod.invoke(repository, query, searchDto);
        
        Method addFacetsMethod = ProductRepositorySolrJImpl.class.getDeclaredMethod("addFacets", SolrQuery.class);
        addFacetsMethod.setAccessible(true);
        addFacetsMethod.invoke(repository, query);
        
        return query;
    }
    
    private static BaseSearchDto createBaseSearchDto() {
        BaseSearchDto dto = new BaseSearchDto();
        dto.setPages(0L);
        dto.setSize(10);
        return dto;
    }
}
