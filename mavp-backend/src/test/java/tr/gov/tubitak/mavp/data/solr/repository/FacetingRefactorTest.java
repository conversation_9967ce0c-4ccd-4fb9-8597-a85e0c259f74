package tr.gov.tubitak.mavp.data.solr.repository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.util.SimpleOrderedMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;

/**
 * Test class to verify faceting behavior during refactoring.
 * This class helps ensure that our refactoring maintains correct functionality.
 */
@ExtendWith(MockitoExtension.class)
class FacetingRefactorTest {

    @Mock
    private SolrBeanConfig solrBeanConfig;

    @Mock
    private CloudHttp2SolrClient solrClient;

    @Mock
    private QueryResponse queryResponse;

    private ProductRepositorySolrJImpl productRepository;

    @BeforeEach
    void setUp() {
        when(solrBeanConfig.getSolrClient()).thenReturn(solrClient);
        productRepository = new ProductRepositorySolrJImpl(solrBeanConfig);
    }

    @Test
    void testBasicFacetingWithoutFilters() throws SolrServerException, IOException {
        // Arrange
        BaseSearchDto searchDto = createBasicSearchDto();
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        // Verify JSON facet configuration
        String jsonFacet = capturedQuery.get("json.facet");
        assertNotNull(jsonFacet, "JSON facet should be configured");
        assertTrue(jsonFacet.contains("offer_market_facet"), "Should include market faceting");
        assertFalse(jsonFacet.contains("offer_depot_facet"), "Should NOT include depot faceting");
        
        System.out.println("=== Basic Faceting Query ===");
        System.out.println("JSON Facet: " + jsonFacet);
        System.out.println("Parent Filter: " + capturedQuery.get("parentFilter"));
        System.out.println("Child Filter: " + capturedQuery.get("childFilter"));
    }

    @Test
    void testDepotFilterImpactOnMarketFacets() throws SolrServerException, IOException {
        // Arrange
        BaseSearchDto searchDto = createBasicSearchDto();
        searchDto.setDepots(Arrays.asList("migros-1234", "a101-5678"));
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        // Verify that depot filtering affects facets
        String jsonFacet = capturedQuery.get("json.facet");
        String parentFilter = capturedQuery.get("parentFilter");
        String childFilter = capturedQuery.get("childFilter");
        
        assertNotNull(parentFilter, "Parent filter should be set");
        assertNotNull(childFilter, "Child filter should be set");
        
        // Check if JSON facet uses parameter substitution
        boolean usesParameterSubstitution = jsonFacet.contains("$parentFilter") || jsonFacet.contains("$childFilter");
        
        System.out.println("=== Depot Filter Impact Test ===");
        System.out.println("JSON Facet: " + jsonFacet);
        System.out.println("Parent Filter: " + parentFilter);
        System.out.println("Child Filter: " + childFilter);
        System.out.println("Uses Parameter Substitution: " + usesParameterSubstitution);
        
        // This is what we want to achieve - facets should be contextually filtered
        if (!usesParameterSubstitution) {
            System.out.println("WARNING: Facets may not be contextually filtered!");
        }
    }

    @Test
    void testMarketFilterWithExcludeTags() throws SolrServerException, IOException {
        // Arrange
        BaseSearchDto searchDto = createBasicSearchDto();
        searchDto.getFilters().put("offer_market", Arrays.asList("migros"));
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        // Verify market filter is applied with correct tag
        String[] filterQueries = capturedQuery.getFilterQueries();
        boolean hasMarketFilter = false;
        
        for (String fq : filterQueries) {
            if (fq.contains("OFFER_MARKET_FILTER_TAG")) {
                hasMarketFilter = true;
                break;
            }
        }
        
        assertTrue(hasMarketFilter, "Should have market filter with correct tag");
        
        // Verify JSON facet excludes the market filter tag
        String jsonFacet = capturedQuery.get("json.facet");
        assertTrue(jsonFacet.contains("OFFER_MARKET_FILTER_TAG"), "Should exclude market filter tag in facets");
        
        System.out.println("=== Market Filter with excludeTags Test ===");
        System.out.println("Filter Queries: " + Arrays.toString(filterQueries));
        System.out.println("JSON Facet: " + jsonFacet);
    }

    @Test
    void testParameterConsistency() throws SolrServerException, IOException {
        // Arrange
        BaseSearchDto searchDto = createBasicSearchDto();
        searchDto.setDepots(Arrays.asList("migros-1234"));
        searchDto.getFilters().put("main_category", Arrays.asList("Çikolata"));
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        String parentFilter = capturedQuery.get("parentFilter");
        String childFilter = capturedQuery.get("childFilter");
        String jsonFacet = capturedQuery.get("json.facet");
        String[] fields = capturedQuery.getFields().split(",");
        
        System.out.println("=== Parameter Consistency Test ===");
        System.out.println("Parent Filter: " + parentFilter);
        System.out.println("Child Filter: " + childFilter);
        System.out.println("JSON Facet: " + jsonFacet);
        System.out.println("Fields: " + Arrays.toString(fields));
        
        // Check consistency across different parts of the query
        boolean parentFilterConsistent = true;
        boolean childFilterConsistent = true;
        
        // Verify that the same filter logic is used everywhere
        if (parentFilter != null) {
            // Check if parent filter is used consistently in filter queries and facets
            String[] filterQueries = capturedQuery.getFilterQueries();
            for (String fq : filterQueries) {
                if (fq.contains("parent which") && !fq.contains(parentFilter.replace("*:* -_nest_path_:*", ""))) {
                    parentFilterConsistent = false;
                    break;
                }
            }
        }
        
        System.out.println("Parent Filter Consistent: " + parentFilterConsistent);
        System.out.println("Child Filter Consistent: " + childFilterConsistent);
        
        // This test helps identify inconsistencies in filter parameter usage
        assertTrue(parentFilterConsistent, "Parent filter should be used consistently");
        assertTrue(childFilterConsistent, "Child filter should be used consistently");
    }

    @Test
    void testCentralizedFilterParameterManagement() {
        // This test verifies that filter parameters are managed centrally
        // We'll check this by examining the code structure after refactoring
        
        System.out.println("=== Centralized Filter Parameter Management Test ===");
        System.out.println("This test should verify that:");
        System.out.println("1. parentFilter and childFilter are defined in one place");
        System.out.println("2. The same parameters are used across query, facets, and projection");
        System.out.println("3. Parameter substitution works correctly in JSON facets");
        
        // After refactoring, we should be able to verify:
        // - Single method that sets up filter parameters
        // - Consistent parameter names and values
        // - Proper parameter substitution in JSON facets
        
        assertTrue(true, "This test will be implemented after refactoring");
    }

    private BaseSearchDto createBasicSearchDto() {
        BaseSearchDto dto = new BaseSearchDto();
        dto.setPages(0L);
        dto.setSize(10);
        dto.setFilters(new HashMap<>());
        return dto;
    }

    private void setupMockQueryResponse() throws SolrServerException, IOException {
        SolrDocumentList documents = new SolrDocumentList();
        documents.setNumFound(10);
        
        SimpleOrderedMap<Object> facets = new SimpleOrderedMap<>();
        facets.add("count", 10);
        
        SimpleOrderedMap<Object> response = new SimpleOrderedMap<>();
        response.add("facets", facets);
        
        when(queryResponse.getResults()).thenReturn(documents);
        when(queryResponse.getResponse()).thenReturn(response);
        when(solrClient.query(any(SolrQuery.class), eq(SolrRequest.METHOD.POST))).thenReturn(queryResponse);
    }
}
