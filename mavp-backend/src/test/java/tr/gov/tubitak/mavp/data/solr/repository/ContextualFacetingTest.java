package tr.gov.tubitak.mavp.data.solr.repository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrRequest;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.CloudHttp2SolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.util.SimpleOrderedMap;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import tr.gov.tubitak.mavp.config.SolrBeanConfig;
import tr.gov.tubitak.mavp.data.api.BaseSearchDto;

/**
 * Test class to verify contextual faceting implementation.
 * Tests that facets are properly filtered based on user-applied filters.
 */
@ExtendWith(MockitoExtension.class)
class ContextualFacetingTest {

    @Mock
    private SolrBeanConfig solrBeanConfig;

    @Mock
    private CloudHttp2SolrClient solrClient;

    @Mock
    private QueryResponse queryResponse;

    private ProductRepositorySolrJImpl productRepository;

    @BeforeEach
    void setUp() {
        when(solrBeanConfig.getSolrClient()).thenReturn(solrClient);
        productRepository = new ProductRepositorySolrJImpl(solrBeanConfig);
    }

    @Test
    void testContextualFacetingWithCategoryFilter() throws SolrServerException, IOException {
        // Arrange: User applies category filter
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setOtherFilters("main_category", Arrays.asList("Çikolata"));
        searchDto.setDepots(Arrays.asList("migros-1234", "a101-5678"));
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        // Verify contextual parent filter includes category filter
        String parentFilter = capturedQuery.get("parentFilter");
        assertNotNull(parentFilter, "Parent filter should be set");
        assertTrue(parentFilter.contains("*:* -_nest_path_:*"), "Should include basic parent filter");
        assertTrue(parentFilter.contains("main_category:\"Çikolata\""), "Should include category filter for contextual faceting");
        
        // Verify JSON facet uses the contextual parent filter
        String jsonFacet = capturedQuery.get("json.facet");
        assertTrue(jsonFacet.contains("$parentFilter"), "JSON facet should use parameter substitution");
        
        System.out.println("=== Contextual Faceting with Category Filter ===");
        System.out.println("Parent Filter: " + parentFilter);
        System.out.println("Expected: Market facets will only show markets that have chocolate products in specified depots");
    }

    @Test
    void testContextualFacetingWithMultipleFilters() throws SolrServerException, IOException {
        // Arrange: User applies multiple filters
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setOtherFilters("main_category", Arrays.asList("Çikolata"));
        searchDto.setOtherFilters("brand", Arrays.asList("Ülker"));
        searchDto.setDepots(Arrays.asList("migros-1234"));
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        String parentFilter = capturedQuery.get("parentFilter");
        assertNotNull(parentFilter, "Parent filter should be set");
        assertTrue(parentFilter.contains("main_category:\"Çikolata\""), "Should include category filter");
        assertTrue(parentFilter.contains("brand:\"Ülker\""), "Should include brand filter");
        
        System.out.println("=== Contextual Faceting with Multiple Filters ===");
        System.out.println("Parent Filter: " + parentFilter);
        System.out.println("Expected: Market facets will only show markets that have Ülker chocolate products in migros-1234");
    }

    @Test
    void testExcludeTagsWithContextualFiltering() throws SolrServerException, IOException {
        // Arrange: User applies market filter (should be excluded from market facets)
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setOtherFilters("offer_market", Arrays.asList("migros"));
        searchDto.setOtherFilters("main_category", Arrays.asList("Çikolata"));
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        // Verify market filter is tagged correctly
        String[] filterQueries = capturedQuery.getFilterQueries();
        boolean hasTaggedMarketFilter = false;
        for (String fq : filterQueries) {
            if (fq.contains("OFFER_MARKET_FILTER_TAG") && fq.contains("offer_market:\"migros\"")) {
                hasTaggedMarketFilter = true;
                break;
            }
        }
        assertTrue(hasTaggedMarketFilter, "Market filter should be tagged for exclusion");
        
        // Verify contextual parent filter includes category but not market (due to excludeTags)
        String parentFilter = capturedQuery.get("parentFilter");
        assertTrue(parentFilter.contains("main_category:\"Çikolata\""), "Should include category filter");
        assertFalse(parentFilter.contains("offer_market"), "Should not include market filter in contextual parent filter");
        
        // Verify JSON facet excludes market filter tag
        String jsonFacet = capturedQuery.get("json.facet");
        assertTrue(jsonFacet.contains("OFFER_MARKET_FILTER_TAG"), "Should exclude market filter tag");
        
        System.out.println("=== ExcludeTags with Contextual Filtering ===");
        System.out.println("Parent Filter: " + parentFilter);
        System.out.println("Expected: Market facets will show all markets (due to excludeTags) but only for chocolate products");
    }

    @Test
    void testBasicFacetingWithoutUserFilters() throws SolrServerException, IOException {
        // Arrange: No user filters, only depot filter
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setDepots(Arrays.asList("migros-1234", "a101-5678"));
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        String parentFilter = capturedQuery.get("parentFilter");
        assertNotNull(parentFilter, "Parent filter should be set");
        assertEquals("*:* -_nest_path_:*", parentFilter, "Should only contain basic parent filter when no user filters applied");
        
        System.out.println("=== Basic Faceting without User Filters ===");
        System.out.println("Parent Filter: " + parentFilter);
        System.out.println("Expected: Market facets will show all markets available in specified depots");
    }

    @Test
    void testQueryComplexityAssessment() throws SolrServerException, IOException {
        // Arrange: Complex scenario with many filters
        BaseSearchDto searchDto = createBaseSearchDto();
        searchDto.setOtherFilters("main_category", Arrays.asList("Çikolata"));
        searchDto.setOtherFilters("sub_category", Arrays.asList("Sütlü Çikolata"));
        searchDto.setOtherFilters("brand", Arrays.asList("Ülker", "Nestle"));
        searchDto.setOtherFilters("refined_quantity_unit", Arrays.asList("gr"));
        searchDto.setDepots(Arrays.asList("migros-1234", "a101-5678", "bim-9999"));
        setupMockQueryResponse();

        // Act
        productRepository.termSearchWithFacets("*:*", searchDto);

        // Assert
        ArgumentCaptor<SolrQuery> queryCaptor = ArgumentCaptor.forClass(SolrQuery.class);
        verify(solrClient).query(queryCaptor.capture(), eq(SolrRequest.METHOD.POST));

        SolrQuery capturedQuery = queryCaptor.getValue();
        
        String parentFilter = capturedQuery.get("parentFilter");
        String[] filterQueries = capturedQuery.getFilterQueries();
        
        System.out.println("=== Query Complexity Assessment ===");
        System.out.println("Parent Filter Length: " + parentFilter.length());
        System.out.println("Number of Filter Queries: " + (filterQueries != null ? filterQueries.length : 0));
        System.out.println("Parent Filter: " + parentFilter);
        
        // Assess complexity
        boolean isComplex = parentFilter.length() > 200 || (filterQueries != null && filterQueries.length > 10);
        System.out.println("Query Complexity: " + (isComplex ? "HIGH" : "MODERATE"));
        
        // Verify all filters are included
        assertTrue(parentFilter.contains("main_category"), "Should include main category");
        assertTrue(parentFilter.contains("sub_category"), "Should include sub category");
        assertTrue(parentFilter.contains("brand"), "Should include brand");
        assertTrue(parentFilter.contains("refined_quantity_unit"), "Should include quantity unit");
    }

    private BaseSearchDto createBaseSearchDto() {
        BaseSearchDto dto = new BaseSearchDto();
        dto.setPages(0L);
        dto.setSize(10);
        return dto;
    }

    private void setupMockQueryResponse() throws SolrServerException, IOException {
        SolrDocumentList documents = new SolrDocumentList();
        documents.setNumFound(10);
        
        SimpleOrderedMap<Object> facets = new SimpleOrderedMap<>();
        facets.add("count", 10);
        
        SimpleOrderedMap<Object> response = new SimpleOrderedMap<>();
        response.add("facets", facets);
        
        when(queryResponse.getResults()).thenReturn(documents);
        when(queryResponse.getResponse()).thenReturn(response);
        when(solrClient.query(any(SolrQuery.class), eq(SolrRequest.METHOD.POST))).thenReturn(queryResponse);
    }
}
