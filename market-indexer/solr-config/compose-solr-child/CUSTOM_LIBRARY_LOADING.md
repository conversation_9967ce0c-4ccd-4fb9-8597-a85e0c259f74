# Solr 9.8.1 Custom Library Loading Guide

## Overview

This document describes the updated custom library loading mechanism for Solr 9.8.1, specifically designed to maintain Turkish language analysis functionality while complying with the new security model.

## Security Model Changes in Solr 9.8.1

### What Changed
- **Deprecated `<lib/>` directives**: While still functional with `solr.config.lib.enabled=true`, they are deprecated and will be removed in Solr 10.0
- **Enhanced security**: Stricter controls on where custom libraries can be loaded from
- **Recommended approach**: Use dedicated lib directories instead of lib directives

### Security Benefits
- **Reduced attack surface**: Limited locations for custom code execution
- **Better isolation**: Clear separation between Solr core and custom libraries
- **Audit trail**: Easier to track and manage custom dependencies

## Current Implementation

### Directory Structure
```
market-indexer/solr-config/compose-solr-child/
├── shared-lib/                          # Custom libraries (NEW)
│   ├── TurkishAnalysis-9.10.0.jar      # Turkish language analysis
│   ├── zemberek-core-0.17.1.jar        # Zemberek morphological analysis
│   ├── zemberek-morphology-0.17.1.jar  # Zemberek morphology
│   ├── zemberek-cekirdek-2.1.3.jar     # Zemberek core (legacy)
│   ├── zemberek-tr-2.1.3.jar           # Zemberek Turkish (legacy)
│   ├── protobuf-java-3.9.0.jar         # Protocol buffers support
│   └── solr-analysis-extras-9.2.1.jar  # Additional analysis components
├── data/
│   └── conf/
│       └── solrconfig.xml               # Updated configuration
└── docker-compose.yml                   # Updated Docker configuration
```

### Docker Configuration

#### Volume Mounts (Security-Compliant)
```yaml
volumes:
  # Solr 9.8.1 Security-Compliant Custom Library Loading
  - ./shared-lib:/var/solr/lib           # Recommended approach
  
  # Configuration files
  - ./data/conf:/opt/solr/server/solr/configsets/_default/conf
  - ./data/solr.xml:/var/solr/data/solr.xml
  - ./data/conf/log4j2.xml:/opt/solr/server/resources/log4j2.xml
  
  # Data persistence
  - solr_data:/var/solr/data
```

#### Environment Variables
```yaml
environment:
  # Enable lib directives for backward compatibility (temporary)
  - SOLR_OPTS=... -Dsolr.config.lib.enabled=true
```

#### Enhanced Startup Command
```bash
command: >
  bash -c '
    echo "=== Solr 9.8.1 Custom Library Setup ==="
    
    # Create necessary directories with proper permissions
    mkdir -p /var/solr/lib
    mkdir -p /var/solr/data
    mkdir -p /var/solr/logs
    
    # Set ownership for all Solr directories
    chown -R solr:solr /var/solr
    
    # Set proper permissions for custom libraries
    if [ -d "/var/solr/lib" ]; then
      chmod -R 644 /var/solr/lib/*.jar 2>/dev/null || true
      chown -R solr:solr /var/solr/lib
      echo "Custom library permissions set successfully"
    fi
    
    echo "=== Starting Solr 9.8.1 ==="
    exec gosu solr solr-foreground
  '
```

### Solr Configuration Updates

#### solrconfig.xml Changes
```xml
<!-- Solr 9.8.1 Security-Compliant Library Loading
     Custom libraries are now loaded via the recommended lib directory approach:
     - /var/solr/lib/ (mounted from ./shared-lib/ in Docker)
     
     The lib directives below are deprecated in Solr 9.8.1 but kept for compatibility.
     They are enabled via -Dsolr.config.lib.enabled=true in SOLR_OPTS.
-->

<!-- Load standard Solr modules (still needed for some built-in functionality) -->
<lib dir="${solr.install.dir}/modules/analysis-extras/lib" regex=".*\.jar" />

<!-- Custom Turkish Analysis libraries are now loaded from /var/solr/lib/
     This directory is mounted from ./shared-lib/ in the Docker configuration.
     Libraries included:
     - TurkishAnalysis-9.10.0.jar
     - zemberek-core-0.17.1.jar
     - zemberek-morphology-0.17.1.jar
     - zemberek-cekirdek-2.1.3.jar
     - zemberek-tr-2.1.3.jar
     - protobuf-java-3.9.0.jar
     - solr-analysis-extras-9.2.1.jar
-->
```

## Turkish Analysis Libraries

### Library Inventory
| Library | Version | Purpose | Status |
|---------|---------|---------|--------|
| TurkishAnalysis-9.10.0.jar | 9.10.0 | Main Turkish analysis | ✅ Active |
| zemberek-core-0.17.1.jar | 0.17.1 | Zemberek core functionality | ✅ Active |
| zemberek-morphology-0.17.1.jar | 0.17.1 | Morphological analysis | ✅ Active |
| zemberek-cekirdek-2.1.3.jar | 2.1.3 | Legacy Zemberek core | ⚠️ Legacy |
| zemberek-tr-2.1.3.jar | 2.1.3 | Legacy Turkish support | ⚠️ Legacy |
| protobuf-java-3.9.0.jar | 3.9.0 | Protocol buffer support | ✅ Active |
| solr-analysis-extras-9.2.1.jar | 9.2.1 | Additional analysis | ✅ Active |

### Functionality Provided
- **Turkish Character Normalization**: Proper handling of Turkish-specific characters (ç, ğ, ı, ö, ş, ü)
- **Morphological Analysis**: Word stem extraction and morphological decomposition
- **Turkish Stop Words**: Language-specific stop word filtering
- **Case Folding**: Turkish-aware case conversion
- **Stemming**: Turkish language stemming algorithms

## Migration Process

### What Was Changed
1. **Library Location**: Moved from `data/lib/` to `shared-lib/` using `git mv` (preserves history)
2. **Docker Mounts**: Updated volume mounts to use `/var/solr/lib` (recommended location)
3. **Permissions**: Enhanced permission management in startup script
4. **Configuration**: Updated solrconfig.xml with security-compliant comments
5. **Documentation**: Added comprehensive documentation and verification scripts

### Git History Preservation
```bash
# Libraries were moved using git mv to preserve history
git mv data/lib/* shared-lib/
```

## Verification and Testing

### Automated Tests
- **test_solr_upgrade.sh**: Comprehensive Solr 9.8.1 upgrade verification
- **test_turkish_analysis.sh**: Turkish analysis library functionality verification

### Manual Verification
```bash
# 1. Check library loading
curl "http://localhost:8981/solr/admin/info/system?wt=json" | jq '.system.classpath'

# 2. Test Turkish analysis
curl "http://localhost:8981/solr/market/analysis/field?analysis.fieldname=product_name&analysis.fieldvalue=çikolata&wt=json"

# 3. Test Turkish search
curl "http://localhost:8981/solr/market/select?q=product_name:çikolata&rows=5&wt=json"
```

## Security Compliance

### Current Approach (Transitional)
- ✅ **Primary**: Uses recommended `/var/solr/lib` directory
- ⚠️ **Fallback**: Keeps deprecated lib directives for compatibility
- ✅ **Enabled**: Uses `solr.config.lib.enabled=true` for transition period

### Future-Proof Approach (Recommended for Solr 10.0)
- ✅ **Remove**: All `<lib/>` directives from solrconfig.xml
- ✅ **Use**: Only lib directory approach (`/var/solr/lib`)
- ✅ **Remove**: `solr.config.lib.enabled=true` system property

## Troubleshooting

### Common Issues

#### 1. Libraries Not Loading
**Symptoms**: Turkish analysis not working, missing classes
**Solution**: 
- Check `/var/solr/lib` directory permissions
- Verify Docker volume mount is correct
- Check Solr logs for ClassNotFoundException

#### 2. Permission Denied Errors
**Symptoms**: Solr startup fails, permission errors in logs
**Solution**:
- Ensure startup script sets proper ownership: `chown -R solr:solr /var/solr`
- Check JAR file permissions: `chmod 644 /var/solr/lib/*.jar`

#### 3. Deprecated Warnings
**Symptoms**: Warnings about deprecated lib directives
**Solution**:
- For immediate use: Keep current configuration (functional)
- For future: Remove lib directives, rely only on lib directory

### Diagnostic Commands
```bash
# Check Solr container logs
docker logs solr1

# Check library directory contents
docker exec solr1 ls -la /var/solr/lib/

# Check library permissions
docker exec solr1 ls -la /var/solr/lib/*.jar

# Test Turkish analysis endpoint
curl "http://localhost:8981/solr/market/analysis/field?analysis.fieldname=product_name&analysis.fieldvalue=test&wt=json"
```

## Performance Considerations

### Library Loading Impact
- **Startup Time**: Minimal impact with proper caching
- **Memory Usage**: ~50MB additional for Turkish analysis libraries
- **Query Performance**: No significant impact on query execution

### Optimization Tips
- **Cache Warming**: Use query warming for Turkish-specific queries
- **Memory Allocation**: Ensure adequate JVM heap for additional libraries
- **Monitoring**: Monitor for any memory leaks in custom libraries

## Future Roadmap

### Solr 10.0 Preparation
1. **Remove lib directives**: Eliminate all `<lib/>` tags from solrconfig.xml
2. **Remove system property**: Remove `solr.config.lib.enabled=true`
3. **Test thoroughly**: Ensure all functionality works with lib directory only
4. **Update documentation**: Remove references to deprecated approaches

### Library Updates
- **TurkishAnalysis**: Consider upgrading to newer versions compatible with Solr 10.0
- **Zemberek**: Evaluate if legacy versions can be removed
- **Dependencies**: Review and update all dependency versions

## Conclusion

The updated custom library loading mechanism for Solr 9.8.1 provides:
- ✅ **Security compliance** with new Solr security model
- ✅ **Backward compatibility** during transition period
- ✅ **Future readiness** for Solr 10.0
- ✅ **Maintained functionality** for Turkish language analysis
- ✅ **Clear migration path** with preserved Git history

This implementation ensures that Turkish language analysis continues to work reliably while following Solr 9.8.1 security best practices.
