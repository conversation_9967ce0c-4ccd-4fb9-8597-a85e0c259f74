<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_yfv64EFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_yfv64UFiEfC3to_rLoZHFQ" bindingContexts="_yfwlgkFiEfC3to_rLoZHFQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AdvancedSolrFacetingService.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrToExcel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ResultType.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrCoreSwapper.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerController.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SpringApplication.class&quot; tooltip=&quot;org.springframework.boot.SpringApplication&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/boot\/spring-boot\/3.2.5\/spring-boot-3.2.5.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework.boot=/=/maven.artifactId=/spring-boot=/=/maven.version=/3.2.5=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.boot(SpringApplication.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpApplication.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportListPDFDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportPDFServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;market-indexer/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepositoryImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductEntity.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentFacetingTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentCodebaseVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;mavp-backend/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ChildOfferModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerFactory.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotPricesDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerModifier.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel2.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotsReadyEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application-dev.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-dev.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-dev.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;categories.txt&quot; tooltip=&quot;mavp-backend/src/main/resources/category/categories.txt&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/category/categories.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrClient.class&quot; tooltip=&quot;org.apache.solr.client.solrj.SolrClient&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.apache.solr.client.solrj(SolrClient.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrIndexer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;managed-schema.xml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MarketIndexerApplication.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotInfo.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AlternativeSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-test.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-test.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-test.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;NestedDocumentVerificationTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-temp.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-temp.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-temp.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrInputDocument.class&quot; tooltip=&quot;org.apache.solr.common.SolrInputDocument&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common(SolrInputDocument.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrTemplate.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CommonParams.class&quot; tooltip=&quot;org.apache.solr.common.params.CommonParams&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common.params(CommonParams.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-dev.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-dev.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-dev.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModelParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ParseEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;StandaloneSolrTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrConnectionTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;category_map.txt&quot; tooltip=&quot;market-indexer/src/main/resources/category/category_map.txt&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/category/category_map.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrDataModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ReferencePipeline.class&quot; tooltip=&quot;java.util.stream.ReferencePipeline&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Library\/Java\/JavaVirtualMachines\/jdk-21.0.6.jdk\/Contents\/Home\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.util.stream(ReferencePipeline.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;c893381 [mavp-backend]&quot; tooltip=&quot;&amp;apos;aaaa&amp;apos; - Commit in repository mavp-backend&quot;>&#xA;&lt;persistable commit=&quot;c8933811cd8efb5e0bdd2936a24bc86de6fe5ca3&quot; path=&quot;/Users/<USER>/developer/springws/mavp-backend/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-prod.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-prod.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-prod.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CorsConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;WebMvcConfigurer.class&quot; tooltip=&quot;org.springframework.web.servlet.config.annotation.WebMvcConfigurer&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-webmvc\/6.1.15\/spring-webmvc-6.1.15.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-webmvc=/=/maven.version=/6.1.15=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.web.servlet.config.annotation(WebMvcConfigurer.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;mavp-backend/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpStringUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AppInitializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SftpConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpFileUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;depot.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/depot.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/depot.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;market-indexer/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;additional-spring-configuration-metadata.json&quot; tooltip=&quot;market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategryMappingFromFile.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;docker-compose.yml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;solrconfig.xml&quot; tooltip=&quot;market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CloudHttp2SolrClient$Builder.class&quot; tooltip=&quot;org.apache.solr.client.solrj.impl.CloudHttp2SolrClient$Builder&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.apache.solr.client.solrj.impl(CloudHttp2SolrClient$Builder.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationEventPublisher.class&quot; tooltip=&quot;org.springframework.context.ApplicationEventPublisher&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context(ApplicationEventPublisher.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationListenerMethodAdapter.class&quot; tooltip=&quot;org.springframework.context.event.ApplicationListenerMethodAdapter&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context.event(ApplicationListenerMethodAdapter.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpFileWatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileDownloadCompletionCallback.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;EventCompletionTracker.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferDepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-test.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-test.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-test.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;7e52ba2 [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;7e52ba21dbe1922e0723e611616263b1b93b0d72&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;ac6482c [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;ac6482cda4d410eedb54cce14c8bc5e9179a4a18&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryMatchingService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;/mruList>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_yfv64UFiEfC3to_rLoZHFQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_yfv64kFiEfC3to_rLoZHFQ" label="%trimmedwindow.label.eclipseSDK" x="204" y="25" width="1512" height="874">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1742906471787"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time>&#xA;&lt;id IMemento.internal.id=&quot;org.eclipse.ui.navigator.ProjectExplorer&quot;/>&#xA;&lt;/show_in_time>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_yfv64kFiEfC3to_rLoZHFQ" selectedElement="_yfv640FiEfC3to_rLoZHFQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_yfv640FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_yfv7BkFiEfC3to_rLoZHFQ">
        <children xsi:type="advanced:Perspective" xmi:id="_yfv65EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_yfv65UFiEfC3to_rLoZHFQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$&#x21e7;&#x2318;L</tags>
          <tags>persp.editorOnboardingCommand:New$$$&#x2318;N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$&#x21e7;&#x2318;T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_yfv65UFiEfC3to_rLoZHFQ" selectedElement="_yfv68UFiEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_yfv65kFiEfC3to_rLoZHFQ" containerData="2500" selectedElement="_yfv650FiEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_yfv650FiEfC3to_rLoZHFQ" containerData="6000" selectedElement="_yfv66EFiEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartStack" xmi:id="_yfv66EFiEfC3to_rLoZHFQ" elementId="left" containerData="6600" selectedElement="_yfv66UFiEfC3to_rLoZHFQ">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv66UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_yfwiVkFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv66kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_yfwiWUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv660FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_yfwiWkFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv67EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_yfwiwEFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_yfv67UFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" containerData="3400" selectedElement="_yfv67kFiEfC3to_rLoZHFQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv67kFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" ref="_yfwixUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_yfv670FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv68EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_yfwiw0FiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_yfv68UFiEfC3to_rLoZHFQ" containerData="7500" selectedElement="_yfv6_UFiEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_yfv68kFiEfC3to_rLoZHFQ" containerData="7500" selectedElement="_yfv680FiEfC3to_rLoZHFQ" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv680FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_yfwiREFiEfC3to_rLoZHFQ"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_yfv69EFiEfC3to_rLoZHFQ" containerData="2500" selectedElement="_yfv69UFiEfC3to_rLoZHFQ">
                  <children xsi:type="basic:PartStack" xmi:id="_yfv69UFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_yfv69kFiEfC3to_rLoZHFQ">
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv69kFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_yfwivUFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_yfv690FiEfC3to_rLoZHFQ" elementId="right" containerData="5000" selectedElement="_yfv6-EFiEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv6-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_yfwiuEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv6-UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_yfwiu0FiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv6-kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_yfwivEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv6-0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_yfwixEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv6_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_yfwiyUFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_yfv6_UFiEfC3to_rLoZHFQ" elementId="bottom" containerData="2500" selectedElement="_yfv7AkFiEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv6_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_yfwiXUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv6_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="_yfwiYEFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7AEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" ref="_yfwiYUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7AUFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_yfwiYkFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7AkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_yfwimUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7A0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_yfwitEFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7BEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_yfwitUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7BUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_yfwiyEFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_yfv7BkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_yfv7B0FiEfC3to_rLoZHFQ" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.debug.ui.DisplayView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.junit.ResultView</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_yfv7B0FiEfC3to_rLoZHFQ" selectedElement="_yfv7CEFiEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_yfv7CEFiEfC3to_rLoZHFQ" containerData="6700" selectedElement="_yfv7EUFiEfC3to_rLoZHFQ" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_yfv7CUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="2500" selectedElement="_yfv7CkFiEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7CkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" ref="_yfwiykFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7C0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_yfwiWkFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7DEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" ref="_yfwjI0FiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Server</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7DUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_yfwiVkFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7DkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_yfwiWUFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7D0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" ref="_yfwiwEFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_yfv7EEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" ref="_yfwiYkFiEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_yfv7EUFiEfC3to_rLoZHFQ" containerData="7500" selectedElement="_yfv7EkFiEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartSashContainer" xmi:id="_yfv7EkFiEfC3to_rLoZHFQ" containerData="5884" selectedElement="_yfv7E0FiEfC3to_rLoZHFQ" horizontal="true">
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7E0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7188" ref="_yfwiREFiEfC3to_rLoZHFQ"/>
                  <children xsi:type="basic:PartStack" xmi:id="_yfv7FEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="2812" selectedElement="_yfv7FkFiEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7FUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" ref="_yfwi4UFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7FkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" ref="_yfwi8kFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7F0FiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" ref="_yfwjM0FiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Version Control (Team)</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7GEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" ref="_yfwjC0FiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7GUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_yfwiuEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7GkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_yfwjIUFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7G0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_yfwivEFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7HEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_yfwiyUFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7HUFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" toBeRendered="false" ref="_yfwixUFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Other</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_yfv7HkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" ref="_yfwjOUFiEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_yfv7H0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="4116" selectedElement="_yfv7IEFiEfC3to_rLoZHFQ">
                  <tags>Git</tags>
                  <tags>Version Control (Team)</tags>
                  <tags>JRebel</tags>
                  <tags>active</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7IEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_yfwimUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7IUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_yfwiXUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7IkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_yfwi4EFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7I0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_yfwitEFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7JEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" ref="_yfwitUFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7JUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_yfwjIkFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7JkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" ref="_yfwjJkFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7J0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_yfwiyEFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Terminal</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_yfv7KEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" ref="_yfwjMEFiEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Git</tags>
                  </children>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartStack" xmi:id="_yfv7KUFiEfC3to_rLoZHFQ" elementId="PartStack@2e2662d8" toBeRendered="false" containerData="3300">
              <children xsi:type="basic:Part" xmi:id="_yfv7KkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" closeable="true">
                <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
                <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
                <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
                <tags>View</tags>
                <tags>inject</tags>
                <tags>categoryTag:JRebel</tags>
                <tags>NoRestore</tags>
                <menus xmi:id="_yfv7K0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart">
                  <tags>ViewMenu</tags>
                  <tags>menuContribution:menu</tags>
                </menus>
                <toolbar xmi:id="_yfv7LEFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" visible="false"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_yfv7LUFiEfC3to_rLoZHFQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_yfv7LkFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_yfwiQUFiEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_yfv7L0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_yfwiQkFiEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_yfv7MEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_yfwiQ0FiEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiQUFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiQkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiQ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_yfwiREFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" selectedElement="_yfwiRUFiEfC3to_rLoZHFQ">
      <children xsi:type="basic:PartStack" xmi:id="_yfwiRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_yfwiRkFiEfC3to_rLoZHFQ">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <children xsi:type="basic:Part" xmi:id="_yfwiRkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductRepositorySolrJImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; partName=&quot;ProductRepositorySolrJImpl.java&quot; title=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3375&quot; selectionTopPixel=&quot;938&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiSkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SearchUtils.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; partName=&quot;SearchUtils.java&quot; title=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;23&quot; selectionOffset=&quot;6070&quot; selectionTopPixel=&quot;1806&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SolrBeanConfig.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; partName=&quot;SolrBeanConfig.java&quot; title=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1098&quot; selectionTopPixel=&quot;98&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiTEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; partName=&quot;GeneralSearchServiceImpl.java&quot; title=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;279&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;8784&quot; selectionTopPixel=&quot;2786&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiTUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BaseSearchDto.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; partName=&quot;BaseSearchDto.java&quot; title=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;6&quot; selectionOffset=&quot;1232&quot; selectionTopPixel=&quot;70&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="DepotRepository.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; partName=&quot;DepotRepository.java&quot; title=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;16&quot; selectionOffset=&quot;217&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchControllerv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; partName=&quot;GeneralSearchControllerv2.java&quot; title=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1597&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiUEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MatcherManager.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; partName=&quot;MatcherManager.java&quot; title=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;929&quot; selectionOffset=&quot;4708&quot; selectionTopPixel=&quot;841&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiUUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductMatcher.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; partName=&quot;ProductMatcher.java&quot; title=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;14&quot; selectionOffset=&quot;5296&quot; selectionTopPixel=&quot;1358&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_yfwiUkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImplv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; partName=&quot;GeneralSearchServiceImplv2.java&quot; title=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4045&quot; selectionTopPixel=&quot;742&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiVkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xA;&lt;xmlDefinedFilters>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/xmlDefinedFilters>&#xA;&lt;/customFilters>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_yfwiV0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwiWEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiWUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiWkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;1&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_yfwiW0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwiXEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xA;&lt;expanded>&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xA;&lt;/expanded>&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;251&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;702&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_yfwiXkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwiX0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiYEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiYUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiYkFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view isPinned=&quot;false&quot;>&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_yfwiY0FiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwidEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwimUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <menus xmi:id="_yfwimkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwinUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwitEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwitUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_yfwitkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwit0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiuEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_yfwiuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwiukFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwivEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwivUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xA;&lt;sorter>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;/sorter>&#xA;&lt;/sorter>&#xA;&lt;filteredTreeFindHistory/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_yfwivkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwiv0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiwEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view failuresOnly=&quot;false&quot; ignoredOnly=&quot;false&quot; layout=&quot;1&quot; orientation=&quot;2&quot; ratio=&quot;500&quot; scroll=&quot;false&quot; sortingCriterion=&quot;1&quot; time=&quot;true&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>highlighted</tags>
      <menus xmi:id="_yfwiwUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwiwkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiw0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwixEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwixUFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_yfwixkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwix0FiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiyEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiyUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwiykFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_yfwiy0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwi1kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwi4EFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwi4UFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_yfwi4kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwi7EFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwi8kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_yfwi80FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwi_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjC0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_yfwjDEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwjFkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjIkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjI0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
      <menus xmi:id="_yfwjJEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwjJUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_yfwjJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwjKUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjMEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
      <menus xmi:id="_yfwjMUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwjMkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Version Control (Team)</tags>
      <menus xmi:id="_yfwjNEFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwjNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjNkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
      <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_yfwjN0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwjOEFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yfwjOUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view search_scope_type=&quot;1&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_yfwjOkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_yfwjO0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_yfwjPEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjPUFiEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_yfwjPkFiEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_yfwjR0FiEfC3to_rLoZHFQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_yfyZMUFiEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjTEFiEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_yfwjTUFiEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_yfwjUEFiEfC3to_rLoZHFQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_yfyXY0FiEfC3to_rLoZHFQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_yfwjUUFiEfC3to_rLoZHFQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_yfyX5EFiEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjUkFiEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_yfwjU0FiEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjdUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjf0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjhkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjh0FiEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjjEFiEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_yfwjjUFiEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjjkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_yfwjlEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_yfyY6kFiEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjmUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CompilationUnitEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjmkFiEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_yfwjm0FiEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjnEFiEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_yfwjnUFiEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_yfwjnkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjoUFiEfC3to_rLoZHFQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjpUFiEfC3to_rLoZHFQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_yfwjrEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjrUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjrkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjr0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_yfwjs0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjtEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_yfwjtUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjtkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjt0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_yfwjuEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <handlers xmi:id="_yfwjuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" command="_yfyZ5kFiEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_yfwjukFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" command="_yfyZ50FiEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_yfwju0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" command="_yfyZ6EFiEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_yfwjvEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" command="_yfyZ6UFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwjvUFiEfC3to_rLoZHFQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_yfwlgkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwjvkFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+SPACE" command="_yfyXQUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjv0FiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+F" command="_yfxyrUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwjwEFiEfC3to_rLoZHFQ" keySequence="SHIFT+F10" command="_yfxzBEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwjwUFiEfC3to_rLoZHFQ" keySequence="ALT+PAGE_UP" command="_yfyX80FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjwkFiEfC3to_rLoZHFQ" keySequence="ALT+PAGE_DOWN" command="_yfyYs0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjw0FiEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_yfxyi0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjxEFiEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_UP" command="_yfyZSkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwjxUFiEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_DOWN" command="_yfyXLUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwjxkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F1" command="_yfxyxEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjx0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F2" command="_yfyYm0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjyEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F3" command="_yfyZPkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjyUFiEfC3to_rLoZHFQ" keySequence="COMMAND+X" command="_yfyXaUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjykFiEfC3to_rLoZHFQ" keySequence="COMMAND+Z" command="_yfyXY0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjy0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Z" command="_yfyX5EFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwjzEFiEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_yfyXJEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjzUFiEfC3to_rLoZHFQ" keySequence="COMMAND+6" command="_yfyXTEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjzkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+I" command="_yfxy6EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwjz0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+L" command="_yfyZaUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj0EFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+D" command="_yfyZk0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj0UFiEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_yfxya0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj0kFiEfC3to_rLoZHFQ" keySequence="COMMAND+A" command="_yfyXuUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj00FiEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_yfyYGUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj1EFiEfC3to_rLoZHFQ" keySequence="ALT+SPACE" command="_yfyZCUFiEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yfwj1UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_yfwlg0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwj1kFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q B" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj10FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_yfwj2EFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q C" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj2UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_yfwj2kFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q D" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj20FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_yfwj3EFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q O" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj3UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_yfwj3kFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q P" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj30FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_yfwj4EFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Q" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwj4UFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q S" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj4kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_yfwj40FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q T" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj5EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_yfwj5UFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q V" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj5kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_yfwj50FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q H" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj6EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_yfwj6UFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q J" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj6kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_yfwj60FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q K" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj7EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_yfwj7UFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q L" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj7kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_yfwj70FiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+SHIFT+T" command="_yfxyikFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj8EFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q X" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj8UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_yfwj8kFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Y" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj80FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_yfwj9EFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Z" command="_yfyYpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwj9UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_yfwj9kFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+B" command="_yfyYrEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj90FiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+P" command="_yfxysUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj-EFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+T" command="_yfyYFEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj-UFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_yfxyg0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj-kFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+A" command="_yfyYwUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj-0FiEfC3to_rLoZHFQ" keySequence="CTRL+Q" command="_yfyZREFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj_EFiEfC3to_rLoZHFQ" keySequence="CTRL+H" command="_yfyZCEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj_UFiEfC3to_rLoZHFQ" keySequence="CTRL+M" command="_yfyZBEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwj_kFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+P" command="_yfyYnUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwj_0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+H" command="_yfyXUkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkAEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+L" command="_yfyYUUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkAUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+M" command="_yfyZiUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkAkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_yfyYg0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkA0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_yfyXRkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkBEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F7" command="_yfyZikFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkBUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F8" command="_yfyXQ0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkBkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F9" command="_yfyXr0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkB0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F10" command="_yfyYK0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkCEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_LEFT" command="_yfxyj0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkCUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_RIGHT" command="_yfyXe0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkCkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F11" command="_yfyZWEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkC0FiEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_yfyYdEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkDEFiEfC3to_rLoZHFQ" keySequence="SHIFT+F5" command="_yfyXy0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkDUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F6" command="_yfyYjUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkDkFiEfC3to_rLoZHFQ" keySequence="ALT+F7" command="_yfyYREFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkD0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F12" command="_yfxywUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkEEFiEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_yfyXskFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkEUFiEfC3to_rLoZHFQ" keySequence="COMMAND+F7" command="_yfyYGkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkEkFiEfC3to_rLoZHFQ" keySequence="COMMAND+F8" command="_yfyXJ0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkE0FiEfC3to_rLoZHFQ" keySequence="COMMAND+F9" command="_yfxy0kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkFEFiEfC3to_rLoZHFQ" keySequence="COMMAND+F11" command="_yfyZhUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkFUFiEfC3to_rLoZHFQ" keySequence="COMMAND+F12" command="_yfyZC0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkFkFiEfC3to_rLoZHFQ" keySequence="F2" command="_yfxycEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkF0FiEfC3to_rLoZHFQ" keySequence="F3" command="_yfxzAkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkGEFiEfC3to_rLoZHFQ" keySequence="F4" command="_yfxyeUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkGUFiEfC3to_rLoZHFQ" keySequence="F5" command="_yfyXhEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkGkFiEfC3to_rLoZHFQ" keySequence="COMMAND+F6" command="_yfxyskFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkG0FiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_yfyZREFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkHEFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_yfxyyEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkHUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X O" command="_yfyYdUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkHkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X P" command="_yfyZoEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkH0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X Q" command="_yfxy5kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkIEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X T" command="_yfyXt0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkIUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X M" command="_yfyXwEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkIkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X B" command="_yfyZ3EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkI0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_UP" command="_yfyYb0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkJEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_DOWN" command="_yfyZykFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkJUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_RIGHT" command="_yfyYWUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkJkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F7" command="_yfyYxUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkJ0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+F12" command="_yfyZm0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkKEFiEfC3to_rLoZHFQ" keySequence="COMMAND+[" command="_yfxyj0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkKUFiEfC3to_rLoZHFQ" keySequence="COMMAND+]" command="_yfyXe0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkKkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Z" command="_yfyYB0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkK0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X R" command="_yfyX40FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkLEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X G" command="_yfyZgUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkLUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X J" command="_yfyYt0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkLkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+[" command="_yfyXREFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwkL0FiEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_yfwkMEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X A" command="_yfxybUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkMUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X E" command="_yfyYmEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkMkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+R" command="_yfyZ4UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkM0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+S" command="_yfyYhEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkNEFiEfC3to_rLoZHFQ" keySequence="COMMAND+3" command="_yfyXLkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkNUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+C" command="_yfyZDUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkNkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_yfyXaEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkN0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_yfyXcUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkOEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+U" command="_yfxy00FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkOUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_yfyZWkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkOkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F" command="_yfyZSEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkO0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+G" command="_yfyYmUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkPEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+W" command="_yfyXZ0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkPUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+H" command="_yfyYDUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkPkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+K" command="_yfxyv0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkP0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+N" command="_yfyYLUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkQEFiEfC3to_rLoZHFQ" keySequence="COMMAND+." command="_yfyZr0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkQUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_yfyZjUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkQkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_yfyYsUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkQ0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+B" command="_yfxywEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkREFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_yfyX6UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkRUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_yfyYAkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkRkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+T" command="_yfyYPUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkR0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+E" command="_yfxyzkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkSEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+V" command="_yfyXpkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkSUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_yfyZvEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkSkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+W" command="_yfyZqkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkS0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+I" command="_yfxyjUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkTEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+J" command="_yfyXVkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkTUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+K" command="_yfyXoEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkTkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+L" command="_yfyXIEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkT0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_yfyZmkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkUEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_yfyXZEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkUUFiEfC3to_rLoZHFQ" keySequence="COMMAND+P" command="_yfyZMUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkUkFiEfC3to_rLoZHFQ" keySequence="COMMAND+S" command="_yfyXnUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkU0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B D" command="_yfyZlkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkVEFiEfC3to_rLoZHFQ" keySequence="COMMAND+U" command="_yfyX30FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkVUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B F" command="_yfxy7EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkVkFiEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_yfyX7UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkV0FiEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_yfyY90FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkWEFiEfC3to_rLoZHFQ" keySequence="COMMAND+K" command="_yfyYrkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkWUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+-" command="_yfyXREFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_yfwkWkFiEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_yfwkW0FiEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_yfyZwUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkXEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_yfxybkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkXUFiEfC3to_rLoZHFQ" keySequence="COMMAND+B" command="_yfxycUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkXkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B R" command="_yfyYXkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkX0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B S" command="_yfyY9EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkYEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+3" command="_yfxyjEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkYUFiEfC3to_rLoZHFQ" keySequence="COMMAND+E" command="_yfyXWEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkYkFiEfC3to_rLoZHFQ" keySequence="COMMAND+F" command="_yfxypkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkY0FiEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_yfxyQUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkZEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D E" command="_yfyZ00FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkZUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D A" command="_yfyZH0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkZkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D T" command="_yfxyTUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkZ0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D J" command="_yfyY4kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkaEFiEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_yfyY90FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkaUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D O" command="_yfyYHkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkakFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D P" command="_yfyZLUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwka0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D Q" command="_yfyX0kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkbEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D B" command="_yfyZkEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkbUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D R" command="_yfyX5UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkbkFiEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_yfyaW0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkb0FiEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_yfyaVkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkcEFiEfC3to_rLoZHFQ" keySequence="COMMAND+BS" command="_yfxyt0FiEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yfwkcUFiEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_yfwlqUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwkckFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+P" command="_yfyZDEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkc0FiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+D" command="_yfyX3kFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwkdEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_yfwlh0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwkdUFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+Q" command="_yfxy7kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkdkFiEfC3to_rLoZHFQ" keySequence="CTRL+." command="_yfyZeEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkd0FiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_yfyXVUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkeEFiEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_MULTIPLY" command="_yfyY10FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkeUFiEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_ADD" command="_yfyZmUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkekFiEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_SUBTRACT" command="_yfyZO0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwke0FiEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_DIVIDE" command="_yfxyxkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkfEFiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_yfyYUkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkfUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_yfyY3UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkfkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_yfyYY0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkf0FiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_yfyZzkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkgEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_UP" command="_yfyZq0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkgUFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_yfyYv0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkgkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_DOWN" command="_yfyXxUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkg0FiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_LEFT" command="_yfyYE0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkhEFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_yfxy7UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkhUFiEfC3to_rLoZHFQ" keySequence="SHIFT+END" command="_yfyYEUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkhkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+INSERT" command="_yfxy1EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkh0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_LEFT" command="_yfyXi0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkiEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_RIGHT" command="_yfyXpEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkiUFiEfC3to_rLoZHFQ" keySequence="SHIFT+HOME" command="_yfyY_UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkikFiEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_yfyZN0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwki0FiEfC3to_rLoZHFQ" keySequence="COMMAND+END" command="_yfyYw0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkjEFiEfC3to_rLoZHFQ" keySequence="END" command="_yfyYw0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkjUFiEfC3to_rLoZHFQ" keySequence="INSERT" command="_yfyYXUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkjkFiEfC3to_rLoZHFQ" keySequence="F2" command="_yfyXL0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkj0FiEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_yfyZdUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkkEFiEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_RIGHT" command="_yfyZV0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkkUFiEfC3to_rLoZHFQ" keySequence="COMMAND+HOME" command="_yfxyakFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkkkFiEfC3to_rLoZHFQ" keySequence="HOME" command="_yfxyakFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkk0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_yfyXp0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwklEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_yfxy20FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwklUFiEfC3to_rLoZHFQ" keySequence="ALT+DEL" command="_yfyXW0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwklkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+DEL" command="_yfyZDkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkl0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Y" command="_yfxyN0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkmEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+X" command="_yfyYIkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkmUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Y" command="_yfyXmEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkmkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_yfyYUkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkm0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+A" command="_yfyYOkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwknEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+J" command="_yfxy40FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwknUFiEfC3to_rLoZHFQ" keySequence="COMMAND++" command="_yfyYkEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwknkFiEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_yfyXm0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkn0FiEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_yfxyS0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkoEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_yfxyUkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkoUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_yfxyS0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkokFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_yfyXS0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwko0FiEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_yfyZqUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkpEFiEfC3to_rLoZHFQ" keySequence="COMMAND+J" command="_yfxykkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkpUFiEfC3to_rLoZHFQ" keySequence="COMMAND+L" command="_yfyZHUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkpkFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_yfyZh0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkp0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_yfyXVUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkqEFiEfC3to_rLoZHFQ" keySequence="COMMAND+D" command="_yfxynkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkqUFiEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_yfyYkEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkqkFiEfC3to_rLoZHFQ" keySequence="SHIFT+CR" command="_yfyZdEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkq0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+CR" command="_yfyZPUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkrEFiEfC3to_rLoZHFQ" keySequence="ALT+BS" command="_yfxyQEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yfwkrUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_yfwljEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwkrkFiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_yfyX4EFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkr0FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_yfyX6UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwksEFiEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_yfxyn0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwksUFiEfC3to_rLoZHFQ" keySequence="COMMAND+F3" command="_yfyZrUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkskFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_UP" command="_yfyX8EFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwks0FiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_yfyXvkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwktEFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+END" command="_yfxymkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwktUFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_UP" command="_yfyX0UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwktkFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_DOWN" command="_yfxyw0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkt0FiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+HOME" command="_yfyXgkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkuEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_yfxyn0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkuUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_yfyYxEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkukFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_yfyZ0kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwku0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_yfyXaEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkvEFiEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_yfyYMEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkvUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_yfyXMEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkvkFiEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_yfyYMEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkv0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_yfyYMEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkwEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+U" command="_yfyY6UFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkwUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_yfyZc0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkwkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_yfyXXkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwkw0FiEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_yfyYOUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkxEFiEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_yfyXeUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkxUFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_yfyXukFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkxkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+'" command="_yfyYS0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkx0FiEfC3to_rLoZHFQ" keySequence="COMMAND+2 F" command="_yfyZmEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkyEFiEfC3to_rLoZHFQ" keySequence="COMMAND+2 R" command="_yfyY-UFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkyUFiEfC3to_rLoZHFQ" keySequence="COMMAND+2 T" command="_yfyYL0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkykFiEfC3to_rLoZHFQ" keySequence="COMMAND+2 L" command="_yfxyk0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwky0FiEfC3to_rLoZHFQ" keySequence="COMMAND+2 M" command="_yfyXh0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwkzEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_yfwlikFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwkzUFiEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_yfyZFEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkzkFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_yfxycEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwkz0FiEfC3to_rLoZHFQ" keySequence="F3" command="_yfyZRUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk0EFiEfC3to_rLoZHFQ" keySequence="F4" command="_yfxyPUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk0UFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_yfyYPkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk0kFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_yfyYQkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk00FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_yfyXkkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk1EFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_yfyZVkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwk1UFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_yfwloUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwk1kFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_yfyYAEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk10FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_yfyXpUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk2EFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_yfyYzkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk2UFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_yfyXnEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk2kFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+N" command="_yfxytEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwk20FiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_yfyYoUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk3EFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_yfyXVEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk3UFiEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_yfxytEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk3kFiEfC3to_rLoZHFQ" keySequence="INSERT" command="_yfyXlUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk30FiEfC3to_rLoZHFQ" keySequence="F4" command="_yfxyiEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk4EFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_yfyY-0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk4UFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_yfyXoUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk4kFiEfC3to_rLoZHFQ" keySequence="ALT+N" command="_yfyXlUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwk40FiEfC3to_rLoZHFQ" keySequence="COMMAND+CR" command="_yfyXUEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwk5EFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_yfwljUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwk5UFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_yfyYAEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk5kFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_yfyXpUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk50FiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+S" command="_yfyXbkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk6EFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_yfyYzkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk6UFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_yfyXnEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk6kFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_yfxyXEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk60FiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_yfyZo0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwk7EFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" bindingContext="_yfwllkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwk7UFiEfC3to_rLoZHFQ" keySequence="CTRL+D" command="_yfyZpkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk7kFiEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_yfyYRkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwk70FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_yfwlhUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwk8EFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_yfxyP0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk8UFiEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_yfyZhEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk8kFiEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_yfyXkEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk80FiEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_yfyY7kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk9EFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_yfyXkEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk9UFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_yfyY7kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk9kFiEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_yfyXkEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwk90FiEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_yfyY7kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yfwk-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_yfwllEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwk-UFiEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_yfyYTUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk-kFiEfC3to_rLoZHFQ" keySequence="F3" command="_yfxyUEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk-0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_yfxyeEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwk_EFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_yfyZc0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwk_UFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_yfxySUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yfwk_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_yfwlnEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwk_0FiEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_yfyZgEFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwlAEFiEfC3to_rLoZHFQ" keySequence="F7" command="_yfyZs0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlAUFiEfC3to_rLoZHFQ" keySequence="F8" command="_yfyYV0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlAkFiEfC3to_rLoZHFQ" keySequence="COMMAND+F2" command="_yfyZEUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlA0FiEfC3to_rLoZHFQ" keySequence="F5" command="_yfxyfkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlBEFiEfC3to_rLoZHFQ" keySequence="F6" command="_yfyXqEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlBUFiEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_yfyYHUFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlBkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_yfwlmUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlB0FiEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_yfyYfkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlCEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_yfwljkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlCUFiEfC3to_rLoZHFQ" keySequence="F1" command="_yfxyTkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlCkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_yfwlpEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlC0FiEfC3to_rLoZHFQ" keySequence="F2" command="_yfxyukFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlDEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_yfwlkUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlDUFiEfC3to_rLoZHFQ" keySequence="F3" command="_yfyYOEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlDkFiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_yfxygUFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwlD0FiEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_yfyYd0FiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwlEEFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_yfyYukFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlEUFiEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_yfyY0EFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlEkFiEfC3to_rLoZHFQ" keySequence="COMMAND+\" command="_yfyYmkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwlE0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_yfyYmkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlFEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_yfyYO0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlFUFiEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_yfyYKkFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_yfwlFkFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_yfyZ3kFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlF0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_yfyZcUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlGEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_yfyZvUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlGUFiEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_yfyZNkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlGkFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_yfyYSkFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlG0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_yfyYKkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlHEFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_yfwlokFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlHUFiEfC3to_rLoZHFQ" keySequence="F5" command="_yfyZcEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlHkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_yfwlo0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlH0FiEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_yfxym0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlIEFiEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_yfyXOEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_yfwloEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlIkFiEfC3to_rLoZHFQ" keySequence="ALT+Y" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlI0FiEfC3to_rLoZHFQ" keySequence="ALT+A" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlJEFiEfC3to_rLoZHFQ" keySequence="ALT+B" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlJUFiEfC3to_rLoZHFQ" keySequence="ALT+C" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlJkFiEfC3to_rLoZHFQ" keySequence="ALT+D" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlJ0FiEfC3to_rLoZHFQ" keySequence="ALT+E" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlKEFiEfC3to_rLoZHFQ" keySequence="ALT+F" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlKUFiEfC3to_rLoZHFQ" keySequence="ALT+G" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlKkFiEfC3to_rLoZHFQ" keySequence="ALT+P" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlK0FiEfC3to_rLoZHFQ" keySequence="ALT+R" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlLEFiEfC3to_rLoZHFQ" keySequence="ALT+S" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlLUFiEfC3to_rLoZHFQ" keySequence="ALT+T" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlLkFiEfC3to_rLoZHFQ" keySequence="ALT+V" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlL0FiEfC3to_rLoZHFQ" keySequence="ALT+W" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlMEFiEfC3to_rLoZHFQ" keySequence="ALT+H" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlMUFiEfC3to_rLoZHFQ" keySequence="ALT+L" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlMkFiEfC3to_rLoZHFQ" keySequence="ALT+N" command="_yfyYKEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_yfwli0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlNEFiEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_yfyZuUFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_yfwlqEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlNkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_yfyZ0kFiEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yfwlN0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="_yfwlm0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlOEFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+D" command="_yfyYskFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlOUFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+P" command="_yfyYykFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlOkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_yfyZsEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlO0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_yfxy-kFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlPEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_yfwllUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlPUFiEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_yfyYMEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlPkFiEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_yfyYMEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlP0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_yfyYMEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_yfwlkEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlQUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_yfxyZUFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlQkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_yfwliUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlQ0FiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_yfxymEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlREFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_yfxyrEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_yfwlmkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlRkFiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_yfyXtEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlR0FiEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_yfyZnEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlSEFiEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_yfyXM0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlSUFiEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_yfyYaEFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlSkFiEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_yfyYk0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_yfwlnUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlTEFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+," command="_yfyZR0FiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlTUFiEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_yfyZAUFiEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_yfwlTkFiEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_yfyZAkFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_yfwliEFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlUEFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_yfyX0EFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlUUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_yfwlj0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlUkFiEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_yfxyZUFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlU0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_yfwln0FiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlVEFiEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_yfxys0FiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlVUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_yfwlhkFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlVkFiEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_yfyYDEFiEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_yfwlV0FiEfC3to_rLoZHFQ" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" bindingContext="_yfwlpUFiEfC3to_rLoZHFQ">
    <bindings xmi:id="_yfwlWEFiEfC3to_rLoZHFQ" keySequence="M1+W" command="_yfyZ6UFiEfC3to_rLoZHFQ">
      <tags>deleted</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yfwlWUFiEfC3to_rLoZHFQ" bindingContext="_yfwlqkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlWkFiEfC3to_rLoZHFQ" bindingContext="_yfwlq0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlW0FiEfC3to_rLoZHFQ" bindingContext="_yfwlrEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlXEFiEfC3to_rLoZHFQ" bindingContext="_yfwlrUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlXUFiEfC3to_rLoZHFQ" bindingContext="_yfwlrkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlXkFiEfC3to_rLoZHFQ" bindingContext="_yfwlr0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlX0FiEfC3to_rLoZHFQ" bindingContext="_yfwlsEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlYEFiEfC3to_rLoZHFQ" bindingContext="_yfwlsUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlYUFiEfC3to_rLoZHFQ" bindingContext="_yfwlskFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlYkFiEfC3to_rLoZHFQ" bindingContext="_yfwls0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlY0FiEfC3to_rLoZHFQ" bindingContext="_yfwltEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlZEFiEfC3to_rLoZHFQ" bindingContext="_yfwltUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlZUFiEfC3to_rLoZHFQ" bindingContext="_yfwltkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlZkFiEfC3to_rLoZHFQ" bindingContext="_yfwlt0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlZ0FiEfC3to_rLoZHFQ" bindingContext="_yfwluEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlaEFiEfC3to_rLoZHFQ" bindingContext="_yfwluUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlaUFiEfC3to_rLoZHFQ" bindingContext="_yfwlukFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlakFiEfC3to_rLoZHFQ" bindingContext="_yfwlu0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwla0FiEfC3to_rLoZHFQ" bindingContext="_yfwlvEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlbEFiEfC3to_rLoZHFQ" bindingContext="_yfwlvUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlbUFiEfC3to_rLoZHFQ" bindingContext="_yfwlvkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlbkFiEfC3to_rLoZHFQ" bindingContext="_yfwlv0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlb0FiEfC3to_rLoZHFQ" bindingContext="_yfwlwEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlcEFiEfC3to_rLoZHFQ" bindingContext="_yfwlwUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlcUFiEfC3to_rLoZHFQ" bindingContext="_yfwlwkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlckFiEfC3to_rLoZHFQ" bindingContext="_yfwlw0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlc0FiEfC3to_rLoZHFQ" bindingContext="_yfwlxEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwldEFiEfC3to_rLoZHFQ" bindingContext="_yfwlxUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwldUFiEfC3to_rLoZHFQ" bindingContext="_yfwlxkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwldkFiEfC3to_rLoZHFQ" bindingContext="_yfwlx0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwld0FiEfC3to_rLoZHFQ" bindingContext="_yfwlyEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwleEFiEfC3to_rLoZHFQ" bindingContext="_yfwlyUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwleUFiEfC3to_rLoZHFQ" bindingContext="_yfwlykFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlekFiEfC3to_rLoZHFQ" bindingContext="_yfwly0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwle0FiEfC3to_rLoZHFQ" bindingContext="_yfwlzEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlfEFiEfC3to_rLoZHFQ" bindingContext="_yfwlzUFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlfUFiEfC3to_rLoZHFQ" bindingContext="_yfwlzkFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlfkFiEfC3to_rLoZHFQ" bindingContext="_yfwlz0FiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlf0FiEfC3to_rLoZHFQ" bindingContext="_yfwl0EFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlgEFiEfC3to_rLoZHFQ" bindingContext="_yfxJAEFiEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_yfwlgUFiEfC3to_rLoZHFQ" bindingContext="_yfxJAUFiEfC3to_rLoZHFQ"/>
  <rootContext xmi:id="_yfwlgkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_yfwlg0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_yfwlhEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_yfwlhUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_yfwlhkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_yfwlh0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_yfwliEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_yfwliUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_yfwlikFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_yfwli0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_yfwljEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_yfwljUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_yfwljkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_yfwlj0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_yfwlkEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_yfwlkUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_yfwlkkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_yfwlk0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="_yfwllEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_yfwllUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_yfwllkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_yfwll0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_yfwlmEFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_yfwlmUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_yfwlmkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_yfwlm0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_yfwlnEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_yfwlnUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_yfwlnkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_yfwln0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_yfwloEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_yfwloUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_yfwlokFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_yfwlo0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_yfwlpEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_yfwlpUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_yfwlpkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_yfwlp0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_yfwlqEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_yfwlqUFiEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_yfwlqkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_yfwlq0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_yfwlrEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_yfwlrUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_yfwlrkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_yfwlr0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_yfwlsEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_yfwlsUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_yfwlskFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_yfwls0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_yfwltEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_yfwltUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_yfwltkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_yfwlt0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_yfwluEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_yfwluUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_yfwlukFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_yfwlu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_yfwlvEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_yfwlvUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_yfwlvkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_yfwlv0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_yfwlwEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_yfwlwUFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_yfwlwkFiEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_yfwlw0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_yfwlxEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_yfwlxUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_yfwlxkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_yfwlx0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_yfwlyEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_yfwlyUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_yfwlykFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_yfwly0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_yfwlzEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_yfwlzUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_yfwlzkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_yfwlz0FiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <rootContext xmi:id="_yfwl0EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_yfxJAEFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_yfxJAUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_yfxJAkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJA0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJBEFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJBUFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJBkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJB0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJCEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJCUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJCkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJC0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJDEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJDUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJDkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJD0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJEEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJEUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJEkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJE0FiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJFEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJFUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJFkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJF0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJGEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJGUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJGkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJG0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJHEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJHUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJHkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJH0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJIEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJIkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJI0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJJEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJJUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJKEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJKUFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJKkFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJK0FiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJLEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJLUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJLkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJL0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJMEFiEfC3to_rLoZHFQ" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJMUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJMkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJNEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJNkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJN0FiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJOEFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJOUFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJOkFiEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJO0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJPEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJPUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJPkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJQUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJQkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJQ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJREFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJRkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJR0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJSEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJSUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJSkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJTEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJTUFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJTkFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJUEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJUUFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_yfxJUkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" category="JRebel" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
    <tags>View</tags>
    <tags>inject</tags>
    <tags>categoryTag:JRebel</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <trimContributions xmi:id="_yfxyJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_yfxyKEFiEfC3to_rLoZHFQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_yfxyKUFiEfC3to_rLoZHFQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_yfxyKkFiEfC3to_rLoZHFQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_yfxyNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyNkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyN0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyOEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxyOUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_yfxyOkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyO0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyPEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyPUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyPkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_yfyah0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyQUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyQkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyQ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyREFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyRkFiEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxyR0FiEfC3to_rLoZHFQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_yfxySEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxySUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxySkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyS0FiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="_yfyag0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyTEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyTUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_yfyajEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyUEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyUUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyUkFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_yfyabUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyU0FiEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyVEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyVUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyVkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyV0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyWEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyWUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyWkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyW0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyXEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_yfyacUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyXkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyX0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyYEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_yfyad0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxyYUFiEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yfxyYkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyY0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyZEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyZkFiEfC3to_rLoZHFQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_yfyaeEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyZ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyaEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyaUFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyakFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxya0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxybEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxybUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxybkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyb0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxycEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxycUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyckFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyc0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxydEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxydUFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxydkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyd0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyeEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyeUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyekFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxye0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_yfyajUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyfEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyfUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyfkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyf0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxygEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxygUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxygkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyg0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyhEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyhUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyhkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxyh0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_yfxyiEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyiUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyikFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_yfyafUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyi0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyjEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyjUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyjkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyj0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxykEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxykUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxykkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyk0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxylEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxylUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxylkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyl0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxymEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_yfyakUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxymUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxymkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxym0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxynEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxynUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxynkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyn0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyoEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyoUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyokFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyo0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_yfyad0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxypEFiEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yfxypUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxypkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyp0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyqEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyqUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxyqkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_yfxyq0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyrEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyrUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyrkFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyr0FiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_yfyaakFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxysEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxysUFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyskFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxys0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxytEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxytUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxytkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyt0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyuEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyukFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyvEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyvUFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxyvkFiEfC3to_rLoZHFQ" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_yfxyv0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxywEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxywUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxywkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyw0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyxEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_yfyakEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyxUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyxkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyx0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyyEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyyUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyykFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyy0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_yfyafEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxyzEFiEfC3to_rLoZHFQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_yfxyzUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyzkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxyz0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy0EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy0UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy0kFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy00FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy1EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy1UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy1kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy10FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy2EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy2UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy2kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy20FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy3EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_yfyajEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy3UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy3kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy30FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy4EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy4UFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy4kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy40FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy5EFiEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxy5UFiEfC3to_rLoZHFQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_yfxy5kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy50FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy6EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy6UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy6kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy60FiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy7EFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy7UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy7kFiEfC3to_rLoZHFQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy70FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy8EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy8UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy8kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy80FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy9EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy9UFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy9kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy90FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_yfyaekFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy-UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy-kFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_yfyaeUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy-0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_yfyagkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfxy_EFiEfC3to_rLoZHFQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_yfxy_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxy_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxzAEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxzAUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxzAkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxzA0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfxzBEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXIEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXIkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXI0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXJEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXJUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXKEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXKUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXKkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXK0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_yfyai0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyXLEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_yfyXLUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXLkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXL0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXMEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXMUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXMkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_yfyacUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXNEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXNkFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXN0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXOEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXOUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXOkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_yfyagEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyXO0FiEfC3to_rLoZHFQ" elementId="url" name="URL"/>
    <parameters xmi:id="_yfyXPEFiEfC3to_rLoZHFQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_yfyXPUFiEfC3to_rLoZHFQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_yfyXPkFiEfC3to_rLoZHFQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_yfyXP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXQUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXQkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXQ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXREFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_yfyagEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyXRUFiEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_yfyXRkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXR0FiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXSEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXSUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXSkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXTEFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="_yfyabEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXTUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXUEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXUUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXUkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXU0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXVEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXVUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_yfyabUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXVkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXV0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXWEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXWUFiEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_yfyaeEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXWkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXW0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXXEFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="_yfyab0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXXkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXX0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXYEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXYUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXYkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXY0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXZEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXZkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXZ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXaEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXaUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXakFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXa0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXbEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXbUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXbkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_yfyacUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXb0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXcEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXcUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXckFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXc0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXdEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_yfyafEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyXdUFiEfC3to_rLoZHFQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_yfyXdkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXd0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXeEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_yfyajEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXeUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXekFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXe0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXfEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXfUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXfkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXf0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXgEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXgUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXgkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXg0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXhEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXhUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXhkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXh0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXiEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXiUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXikFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXi0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXjEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXjUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXjkFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="_yfyag0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXj0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXkEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_yfyah0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXkUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXkkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXk0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_yfyad0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyXlEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_yfyXlUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXlkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXl0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXmEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXmUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXmkFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXm0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXnEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXnUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXnkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXn0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXoEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXoUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXokFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_yfyaakFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXo0FiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXpEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXpUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXpkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXp0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXqEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXqUFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXqkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_yfyafEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyXq0FiEfC3to_rLoZHFQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_yfyXrEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXrUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXrkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXr0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXsEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXsUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXskFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXs0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXtEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXtUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXtkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXt0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXuEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXukFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_yfyajUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyXvEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_yfyXvUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_yfyXvkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXv0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXwEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXwUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXwkFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXw0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXxEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXxUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXxkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXx0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXyEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXyUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXykFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXy0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXzEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXzUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXzkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyXz0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_yfyaj0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX0EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX0UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX0kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX00FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX1EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_yfyagEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyX1UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_yfyX1kFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX10FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX2EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_yfyagkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyX2UFiEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_yfyX2kFiEfC3to_rLoZHFQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_yfyX20FiEfC3to_rLoZHFQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_yfyX3EFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX3UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX3kFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX30FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX4EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX4UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX4kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX40FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX5EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX5UFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX5kFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX50FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX6EFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX6UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX6kFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX60FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_yfyafUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX7EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX7UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX7kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX70FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX8EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX8UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyX8kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_yfyX80FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX9EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX9UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_yfyad0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyX9kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_yfyX90FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX-UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_yfyaikFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyX-kFiEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_yfyX-0FiEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_yfyX_EFiEfC3to_rLoZHFQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_yfyX_UFiEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_yfyX_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyX_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYAEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYAUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYAkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYA0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYBEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYBUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYBkFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYB0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYCEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYCUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYCkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYC0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYDEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYDUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYDkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYD0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYEEFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_yfyackFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYEUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYEkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYE0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYFEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_yfyafUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYFUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_yfyagEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYFkFiEfC3to_rLoZHFQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_yfyYF0FiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYGEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYGUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYGkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYG0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYHEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYHUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYHkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYH0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYIEFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="_yfyag0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYIkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYI0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYJEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYJUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYKEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_yfyah0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYKUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYKkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYK0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYLEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYLUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYLkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYL0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYMEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYMUFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYMkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYNEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYNkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYN0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYOEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYOUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYOkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYO0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYPEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYPUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYPkFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYQUFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="_yfyab0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYQkFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYQ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYREFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYRkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYR0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_yfyai0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYSEFiEfC3to_rLoZHFQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_yfyYSUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYSkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYTEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYTUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYTkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYUEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYUUFiEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_yfyaf0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYUkFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_yfyabUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYU0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYVEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYVUFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYVkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYV0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYWEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYWUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYWkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYW0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYXEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYXkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYX0FiEfC3to_rLoZHFQ" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYYEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_yfyYYUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_yfyYYkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYY0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYZEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYZkFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYZ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_yfyYaEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYaUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYakFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYa0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYbEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYbUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYbkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYb0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYcEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYcUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYckFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYc0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYdEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYdUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYdkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYd0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYeEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYeUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYekFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYe0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYfEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_yfyajEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYfUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYfkFiEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_yfyaeEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYf0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYgEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_yfyabkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYgUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_yfyYgkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYg0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYhEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYhUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYhkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYh0FiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="_yfyab0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYiEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYiUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYikFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYi0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYjEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYjUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYjkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYj0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYkEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYkUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYkkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYk0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYlEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYlUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYlkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYl0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYmEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYmUFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYmkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYm0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_yfyakEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYnEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYnUFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYnkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYn0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYoEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYoUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYokFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYo0FiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="_yfyab0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYpEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_yfyaa0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYpUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_yfyYpkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_yfyYp0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_yfyYqEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYqUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYqkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_yfyad0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYq0FiEfC3to_rLoZHFQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_yfyYrEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYrUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYrkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYr0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYsEFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="_yfyab0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYsUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYskFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_yfyaeUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYs0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYtEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYtUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_yfyadkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYtkFiEfC3to_rLoZHFQ" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_yfyYt0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYuEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_yfyakUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYukFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_yfyagkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYvEFiEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_yfyYvUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYvkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYv0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYwEFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYwUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyYwkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_yfyYw0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYxEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYxUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYxkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYx0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYyEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYyUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYykFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_yfyaeUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYy0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYzEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYzUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYzkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyYz0FiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY0EFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY0UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY0kFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_yfyafUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY00FiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="_yfyahUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY1EFiEfC3to_rLoZHFQ" elementId="java.execute.workspaceCommand" commandName="Execute Java Command in Workspace" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyY1UFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_yfyY1kFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_yfyY10FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY2EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY2UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY2kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY20FiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY3EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY3UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY3kFiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY30FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY4EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY4UFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY4kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY40FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY5EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY5UFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY5kFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY50FiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY6EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY6UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY6kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY60FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY7EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY7UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY7kFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_yfyah0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY70FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY8EFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY8UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_yfyajUFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyY8kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_yfyY80FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import from a Source Repository" description="Imports a plug-in from a source repository" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY9EFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY9UFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY9kFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY90FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY-EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY-UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY-kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY-0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY_EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY_UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY_kFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyY_0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZAEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZAUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZAkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZA0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZBEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZBUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZBkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZB0FiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZCEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZCUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZCkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZC0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZDEFiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZDUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZDkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZD0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZEEFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="_yfyahUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZEUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZEkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZE0FiEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZFEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZFUFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZFkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZF0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZGEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZGUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZGkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_yfyajEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZG0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_yfyZHEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_yfyZHUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZHkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZH0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZIEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZIUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZIkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZI0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZJEFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZJUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZJkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZJ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZKEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZKUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZKkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZK0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZLEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZLUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZLkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZL0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZMEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_yfyajUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZMUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZMkFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZM0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZNEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZNUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZNkFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZN0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZOEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZOUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZOkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZO0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZPEFiEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZPUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZPkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZP0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZQEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZQUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZQkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZQ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZREFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZRUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZRkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZR0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZSEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZSUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZSkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZS0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZTEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_yfyaikFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZTUFiEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_yfyZTkFiEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_yfyZT0FiEfC3to_rLoZHFQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_yfyZUEFiEfC3to_rLoZHFQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_yfyZUUFiEfC3to_rLoZHFQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_yfyZUkFiEfC3to_rLoZHFQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_yfyZU0FiEfC3to_rLoZHFQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_yfyZVEFiEfC3to_rLoZHFQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_yfyZVUFiEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_yfyZVkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZV0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZWEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZWUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZWkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZW0FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZXEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZXkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZX0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZYEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZYUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_yfyajEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZYkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZY0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZZEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZZkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZZ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_yfyZaEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_yfyZaUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZakFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZa0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZbEFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZbUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_yfyad0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZbkFiEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yfyZb0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZcEFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_yfyaa0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZcUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZckFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZc0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZdEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZdUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZdkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZd0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZeEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZeUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZekFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_yfyaakFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZe0FiEfC3to_rLoZHFQ" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="_yfyZfEFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="_yfyZfUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="_yfyZfkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_yfyahEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZf0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZgEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZgUFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZgkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZg0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZhEFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_yfyah0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZhUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZhkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZh0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZiEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZiUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZikFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZi0FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZjEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZjUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZjkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZj0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZkEFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZkUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZkkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZk0FiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZlEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZlUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_yfyafUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZlkFiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZl0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZmEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZmUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZmkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZm0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZnEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZnUFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZnkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZn0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZoEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZoUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZokFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZo0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_yfyadkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZpEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZpUFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_yfyackFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZpkFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZp0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZqEFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZqUFiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_yfyae0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZqkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZq0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZrEFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZrUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZrkFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZr0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZsEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_yfyaeUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZsUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZskFiEfC3to_rLoZHFQ" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_yfyZs0FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZtEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_yfyagEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZtUFiEfC3to_rLoZHFQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_yfyZtkFiEfC3to_rLoZHFQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_yfyZt0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_yfyagkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZuEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_yfyafEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZuUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZukFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZu0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZvEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZvUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZvkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZv0FiEfC3to_rLoZHFQ" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZwEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZwUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_yfyafEFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZwkFiEfC3to_rLoZHFQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_yfyZw0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZxEFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZxUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZxkFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZx0FiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZyEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZyUFiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZykFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_yfyadUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZy0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZzEFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZzUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_yfyaiEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZzkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZz0FiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZ0EFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_yfyZ0UFiEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_yfyZ0kFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_yfyad0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ00FiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ1EFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_yfyagUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ1UFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_yfyajkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ1kFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_yfyafkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ10FiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ2EFiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="_yfyai0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ2UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ2kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ20FiEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ3EFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="_yfyaiUFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ3UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_yfyadEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ3kFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_yfyabkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ30FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ4EFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ4UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_yfyad0FiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZ4kFiEfC3to_rLoZHFQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_yfyZ40FiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="_yfyakkFiEfC3to_rLoZHFQ">
    <parameters xmi:id="_yfyZ5EFiEfC3to_rLoZHFQ" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="_yfyZ5UFiEfC3to_rLoZHFQ" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="_yfyZ5kFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.arrangeWindowsInFront" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.arrangeWindows.name" description="%command.arrangeWindows.desc" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ50FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.minimizeWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.minimize.name" description="%command.minimize.desc" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ6EFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.zoomWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.zoom.name" description="%command.zoom.desc" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ6UFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.closeDialog" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.closeDialog.name" description="%command.closeDialog.desc" category="_yfyagEFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ6kFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_yfyZ60FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ7EFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ7UFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ7kFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ70FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ8EFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ8UFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ8kFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ80FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ9EFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ9UFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ9kFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ90FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ-EFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ-UFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ-kFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ-0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ_EFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ_UFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ_kFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyZ_0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaAEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaAUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaAkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaA0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaBEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaBUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaBkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaB0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaCEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaCUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaCkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaC0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaDEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaDUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaDkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaD0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaEEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaEUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaEkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaE0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaFEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaFUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaFkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaF0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaGEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaGUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaGkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaG0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaHEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaHUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaHkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaH0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaIEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaIUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaIkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaI0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaJEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaJUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaJkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaJ0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaKEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaKUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaKkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaK0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaLEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaLUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaLkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaL0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaMEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaMUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaMkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaM0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaNEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaNUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaNkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaN0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaOEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaOUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaOkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaO0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaPEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaPUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaPkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaP0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaQEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaQUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaQkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaQ0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaREFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaRUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaRkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaR0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaSEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaSUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaSkFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaS0FiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaTEFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaTUFiEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaTkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelStartup" commandName="JRebel Configuration Startup Page" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaT0FiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_yfyaa0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaUEFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebel" commandName="JRebel Configuration" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaUUFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelProjects" commandName="JRebel Configuration Projects Page" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaUkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.openSetupGuide" commandName="JRebel Setup Guide" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaU0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebel" commandName="Activate JRebel" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaVEFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaVUFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelRemoteServers" commandName="JRebel Configuration Remote Servers Page" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaVkFiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_yfyak0FiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaV0FiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.jrebelSupportPopup" commandName="JRebel Support" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaWEFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelAdvanced" commandName="JRebel Configuration Advanced Page" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaWUFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.remoting.synchronize" commandName="Synchronize" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaWkFiEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebelPopup" commandName="Activate JRebel" category="_yfyakkFiEfC3to_rLoZHFQ"/>
  <commands xmi:id="_yfyaW0FiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_yfyak0FiEfC3to_rLoZHFQ"/>
  <addons xmi:id="_yfyaXEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_yfyaXUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_yfyaXkFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_yfyaX0FiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_yfyaYEFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_yfyaYUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_yfyaYkFiEfC3to_rLoZHFQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_yfyaY0FiEfC3to_rLoZHFQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_yfyaZEFiEfC3to_rLoZHFQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_yfyaZUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_yfyaZkFiEfC3to_rLoZHFQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_yfyaZ0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_yfyaaEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_yfyaaUFiEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler"/>
  <categories xmi:id="_yfyaakFiEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_yfyaa0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_yfyabEFiEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="_yfyabUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_yfyabkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_yfyab0FiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="_yfyacEFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_yfyacUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_yfyackFiEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_yfyac0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_yfyadEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_yfyadUFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_yfyadkFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_yfyad0FiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_yfyaeEFiEfC3to_rLoZHFQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_yfyaeUFiEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_yfyaekFiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_yfyae0FiEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_yfyafEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_yfyafUFiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_yfyafkFiEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_yfyaf0FiEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_yfyagEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_yfyagUFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_yfyagkFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_yfyag0FiEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="_yfyahEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_yfyahUFiEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="_yfyahkFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_yfyah0FiEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_yfyaiEFiEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_yfyaiUFiEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_yfyaikFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_yfyai0FiEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_yfyajEFiEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_yfyajUFiEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_yfyajkFiEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_yfyaj0FiEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_yfyakEFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_yfyakUFiEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_yfyakkFiEfC3to_rLoZHFQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_yfyak0FiEfC3to_rLoZHFQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
</application:Application>
