<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_tIMpMEYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_tIMpMUYJEfC3to_rLoZHFQ" bindingContexts="_tIN5yUYJEfC3to_rLoZHFQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrCoreSwapper.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SpringApplication.class&quot; tooltip=&quot;org.springframework.boot.SpringApplication&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/boot\/spring-boot\/3.2.5\/spring-boot-3.2.5.jar&amp;lt;org.springframework.boot(SpringApplication.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;solrconfig.xml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/data/conf/solrconfig.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/data/conf/solrconfig.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;docker-compose.yml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductEntity.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;managed-schema.xml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AdvancedSolrFacetingService.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrToExcel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ResultType.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerController.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpApplication.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportListPDFDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportPDFServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;market-indexer/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepositoryImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentFacetingTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentCodebaseVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;mavp-backend/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ChildOfferModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerFactory.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotPricesDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerModifier.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel2.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotsReadyEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application-dev.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-dev.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-dev.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;categories.txt&quot; tooltip=&quot;mavp-backend/src/main/resources/category/categories.txt&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/category/categories.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrClient.class&quot; tooltip=&quot;org.apache.solr.client.solrj.SolrClient&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.apache.solr.client.solrj(SolrClient.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrIndexer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MarketIndexerApplication.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotInfo.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AlternativeSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-test.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-test.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-test.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;NestedDocumentVerificationTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-temp.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-temp.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-temp.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrInputDocument.class&quot; tooltip=&quot;org.apache.solr.common.SolrInputDocument&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common(SolrInputDocument.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrTemplate.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CommonParams.class&quot; tooltip=&quot;org.apache.solr.common.params.CommonParams&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common.params(CommonParams.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-dev.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-dev.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-dev.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModelParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ParseEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;StandaloneSolrTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrConnectionTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;category_map.txt&quot; tooltip=&quot;market-indexer/src/main/resources/category/category_map.txt&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/category/category_map.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrDataModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ReferencePipeline.class&quot; tooltip=&quot;java.util.stream.ReferencePipeline&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Library\/Java\/JavaVirtualMachines\/jdk-21.0.6.jdk\/Contents\/Home\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.util.stream(ReferencePipeline.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;c893381 [mavp-backend]&quot; tooltip=&quot;&amp;apos;aaaa&amp;apos; - Commit in repository mavp-backend&quot;>&#xA;&lt;persistable commit=&quot;c8933811cd8efb5e0bdd2936a24bc86de6fe5ca3&quot; path=&quot;/Users/<USER>/developer/springws/mavp-backend/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-prod.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-prod.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-prod.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CorsConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;WebMvcConfigurer.class&quot; tooltip=&quot;org.springframework.web.servlet.config.annotation.WebMvcConfigurer&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-webmvc\/6.1.15\/spring-webmvc-6.1.15.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-webmvc=/=/maven.version=/6.1.15=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.web.servlet.config.annotation(WebMvcConfigurer.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;mavp-backend/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpStringUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AppInitializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SftpConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpFileUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;depot.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/depot.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/depot.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;market-indexer/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;additional-spring-configuration-metadata.json&quot; tooltip=&quot;market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategryMappingFromFile.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;solrconfig.xml&quot; tooltip=&quot;market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CloudHttp2SolrClient$Builder.class&quot; tooltip=&quot;org.apache.solr.client.solrj.impl.CloudHttp2SolrClient$Builder&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.apache.solr.client.solrj.impl(CloudHttp2SolrClient$Builder.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationEventPublisher.class&quot; tooltip=&quot;org.springframework.context.ApplicationEventPublisher&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context(ApplicationEventPublisher.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationListenerMethodAdapter.class&quot; tooltip=&quot;org.springframework.context.event.ApplicationListenerMethodAdapter&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context.event(ApplicationListenerMethodAdapter.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpFileWatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileDownloadCompletionCallback.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;EventCompletionTracker.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferDepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-test.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-test.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-test.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;7e52ba2 [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;7e52ba21dbe1922e0723e611616263b1b93b0d72&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;ac6482c [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;ac6482cda4d410eedb54cce14c8bc5e9179a4a18&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;/mruList>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_tIMpMUYJEfC3to_rLoZHFQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_tIMpMkYJEfC3to_rLoZHFQ" label="%trimmedwindow.label.eclipseSDK" x="204" y="25" width="1512" height="874">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1742906471787"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time>&#xA;&lt;id IMemento.internal.id=&quot;org.eclipse.ui.navigator.ProjectExplorer&quot;/>&#xA;&lt;/show_in_time>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpMkYJEfC3to_rLoZHFQ" selectedElement="_tIMpM0YJEfC3to_rLoZHFQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_tIMpM0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_tIMpVkYJEfC3to_rLoZHFQ">
        <children xsi:type="advanced:Perspective" xmi:id="_tIMpNEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_tIMpNUYJEfC3to_rLoZHFQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$&#x21e7;&#x2318;L</tags>
          <tags>persp.editorOnboardingCommand:New$$$&#x2318;N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$&#x21e7;&#x2318;T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpNUYJEfC3to_rLoZHFQ" selectedElement="_tIMpQUYJEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpNkYJEfC3to_rLoZHFQ" containerData="2500" selectedElement="_tIMpN0YJEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpN0YJEfC3to_rLoZHFQ" containerData="6000" selectedElement="_tIMpOEYJEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartStack" xmi:id="_tIMpOEYJEfC3to_rLoZHFQ" elementId="left" containerData="6600" selectedElement="_tIMpOUYJEfC3to_rLoZHFQ">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpOUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_tINSi0YJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpOkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_tINSjkYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpO0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_tINSj0YJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpPEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_tINTJUYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_tIMpPUYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" containerData="3400" selectedElement="_tIMpPkYJEfC3to_rLoZHFQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpPkYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" ref="_tINTKkYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_tIMpP0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpQEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_tINTKEYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpQUYJEfC3to_rLoZHFQ" containerData="7500" selectedElement="_tIMpTUYJEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpQkYJEfC3to_rLoZHFQ" containerData="7500" selectedElement="_tIMpQ0YJEfC3to_rLoZHFQ" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpQ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_tINSaEYJEfC3to_rLoZHFQ"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpREYJEfC3to_rLoZHFQ" containerData="2500" selectedElement="_tIMpRUYJEfC3to_rLoZHFQ">
                  <children xsi:type="basic:PartStack" xmi:id="_tIMpRUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_tIMpRkYJEfC3to_rLoZHFQ">
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpRkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_tINTIkYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_tIMpR0YJEfC3to_rLoZHFQ" elementId="right" containerData="5000" selectedElement="_tIMpSEYJEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpSEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_tINTHUYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpSUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_tINTIEYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpSkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_tINTIUYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpS0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_tINTKUYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpTEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_tINTLkYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_tIMpTUYJEfC3to_rLoZHFQ" elementId="bottom" containerData="2500" selectedElement="_tIMpUkYJEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpTkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_tINSqUYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpT0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="_tINSrEYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpUEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" ref="_tINSrUYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpUUYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_tINSrkYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpUkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_tINS_kYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpU0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_tINTGUYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpVEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_tINTGkYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpVUYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_tINTLUYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_tIMpVkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_tIMpV0YJEfC3to_rLoZHFQ" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.debug.ui.DisplayView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.junit.ResultView</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpV0YJEfC3to_rLoZHFQ" selectedElement="_tIMpWEYJEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpWEYJEfC3to_rLoZHFQ" containerData="6700" selectedElement="_tIMpYUYJEfC3to_rLoZHFQ" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_tIMpWUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="2500" selectedElement="_tIMpWkYJEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpWkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" ref="_tINTL0YJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpW0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_tINSj0YJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpXEYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" ref="_tIN3VUYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Server</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpXUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_tINSi0YJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpXkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_tINSjkYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpX0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" ref="_tINTJUYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_tIMpYEYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" ref="_tINSrkYJEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpYUYJEfC3to_rLoZHFQ" containerData="7500" selectedElement="_tIMpYkYJEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartSashContainer" xmi:id="_tIMpYkYJEfC3to_rLoZHFQ" containerData="5884" selectedElement="_tIMpY0YJEfC3to_rLoZHFQ" horizontal="true">
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpY0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7188" ref="_tINSaEYJEfC3to_rLoZHFQ"/>
                  <children xsi:type="basic:PartStack" xmi:id="_tIMpZEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="2812" selectedElement="_tIMpZkYJEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpZUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" ref="_tINTRkYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpZkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" ref="_tINTV0YJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpZ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" ref="_tIN3ckYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Version Control (Team)</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpaEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" ref="_tINTcEYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpaUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_tINTHUYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpakYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_tIN3U0YJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpa0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_tINTIUYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpbEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_tINTLkYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpbUYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" toBeRendered="false" ref="_tINTKkYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Other</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_tIMpbkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" ref="_tIN3eEYJEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_tIMpb0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="4116" selectedElement="_tIMpcEYJEfC3to_rLoZHFQ">
                  <tags>Git</tags>
                  <tags>Version Control (Team)</tags>
                  <tags>JRebel</tags>
                  <tags>active</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpcEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_tINS_kYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpcUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_tINSqUYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpckYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_tINTRUYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpc0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_tINTGUYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpdEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" ref="_tINTGkYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpdUYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_tIN3VEYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpdkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" ref="_tIN3WEYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpd0YJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_tINTLUYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Terminal</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_tIMpeEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" ref="_tIN3YkYJEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Git</tags>
                  </children>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartStack" xmi:id="_tIMpeUYJEfC3to_rLoZHFQ" elementId="PartStack@2e2662d8" toBeRendered="false" containerData="3300">
              <children xsi:type="basic:Part" xmi:id="_tIMpekYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" closeable="true">
                <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
                <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
                <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
                <tags>View</tags>
                <tags>inject</tags>
                <tags>categoryTag:JRebel</tags>
                <tags>NoRestore</tags>
                <menus xmi:id="_tIMpe0YJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart">
                  <tags>ViewMenu</tags>
                  <tags>menuContribution:menu</tags>
                </menus>
                <toolbar xmi:id="_tINQQEYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" visible="false"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_tINQQUYJEfC3to_rLoZHFQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_tINQQkYJEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_tINSZUYJEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_tINQQ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_tINSZkYJEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_tINQREYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_tINSZ0YJEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSZUYJEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSZkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSZ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_tINSaEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" selectedElement="_tINSaUYJEfC3to_rLoZHFQ">
      <children xsi:type="basic:PartStack" xmi:id="_tINSaUYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_tINSh0YJEfC3to_rLoZHFQ">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <children xsi:type="basic:Part" xmi:id="_tINSakYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductRepositorySolrJImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; partName=&quot;ProductRepositorySolrJImpl.java&quot; title=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;9643&quot; selectionTopPixel=&quot;3094&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSbkYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SolrBeanConfig.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; partName=&quot;SolrBeanConfig.java&quot; title=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1098&quot; selectionTopPixel=&quot;98&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSb0YJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; partName=&quot;GeneralSearchServiceImpl.java&quot; title=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;84&quot; selectionOffset=&quot;2306&quot; selectionTopPixel=&quot;378&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSc0YJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BaseSearchDto.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; partName=&quot;BaseSearchDto.java&quot; title=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;6&quot; selectionOffset=&quot;1232&quot; selectionTopPixel=&quot;70&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSdEYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="DepotRepository.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; partName=&quot;DepotRepository.java&quot; title=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;16&quot; selectionOffset=&quot;217&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSdUYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchControllerv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; partName=&quot;GeneralSearchControllerv2.java&quot; title=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1597&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSdkYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MatcherManager.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; partName=&quot;MatcherManager.java&quot; title=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;929&quot; selectionOffset=&quot;4708&quot; selectionTopPixel=&quot;841&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSd0YJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductMatcher.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; partName=&quot;ProductMatcher.java&quot; title=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;14&quot; selectionOffset=&quot;5296&quot; selectionTopPixel=&quot;1358&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSeEYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="docker-compose.yml" iconURI="platform:/plugin/org.eclipse.ui.genericeditor/icons/full/obj16/generic_editor.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;docker-compose.yml&quot; partName=&quot;docker-compose.yml&quot; title=&quot;docker-compose.yml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;518&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.ui.genericeditor.GenericEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSfEYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="IndexerService.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerService.java&quot; partName=&quot;IndexerService.java&quot; title=&quot;IndexerService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3125&quot; selectionTopPixel=&quot;2657&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSgEYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Compare docker-compose.yml Current and Index" iconURI="platform:/plugin/org.eclipse.compare/icons/full/eview16/compare_view.png" closeable="true">
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.compare.CompareEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_tINSh0YJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SolrCoreSwapper.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrCoreSwapper.java&quot; partName=&quot;SolrCoreSwapper.java&quot; title=&quot;SolrCoreSwapper.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;873&quot; selectionTopPixel=&quot;140&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSi0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xA;&lt;xmlDefinedFilters>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/xmlDefinedFilters>&#xA;&lt;/customFilters>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_tINSjEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINSjUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSjkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSj0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;1&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_tINSkEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINSpEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSqUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xA;&lt;expanded>&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xA;&lt;/expanded>&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;251&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;702&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_tINSqkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINSq0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSrEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSrUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINSrkYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view isPinned=&quot;false&quot;>&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.jdt.ui.JavaSearchResultPage&quot; org.eclipse.jdt.search.resultpage.grouping=&quot;4&quot; org.eclipse.jdt.search.resultpage.limit=&quot;1000&quot; org.eclipse.jdt.search.resultpage.limit_enabled=&quot;TRUE&quot; org.eclipse.jdt.search.resultpage.sorting=&quot;1&quot; org.eclipse.search.lastActivation=&quot;2&quot; org.eclipse.search.resultpage.layout=&quot;2&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_tINSr0YJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINSwUYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINS_kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <menus xmi:id="_tINS_0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTAkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTGUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTGkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_tINTG0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTHEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTHUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_tINTHkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTH0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTIEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTIUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTIkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xA;&lt;sorter>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;/sorter>&#xA;&lt;/sorter>&#xA;&lt;filteredTreeFindHistory/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_tINTI0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTJEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTJUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view failuresOnly=&quot;false&quot; ignoredOnly=&quot;false&quot; layout=&quot;1&quot; orientation=&quot;2&quot; ratio=&quot;500&quot; scroll=&quot;false&quot; sortingCriterion=&quot;1&quot; time=&quot;true&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>highlighted</tags>
      <menus xmi:id="_tINTJkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTJ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTKEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTKUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTKkYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_tINTK0YJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTLEYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTLUYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTLkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTL0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_tINTMEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTO0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTRUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTRkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_tINTR0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTUUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTV0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_tINTWEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTYkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tINTcEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_tINTcUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tINTe0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tIN3U0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tIN3VEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tIN3VUYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
      <menus xmi:id="_tIN3VkYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tIN3V0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tIN3WEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_tIN3WUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tIN3W0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tIN3YkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
      <menus xmi:id="_tIN3Y0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tIN3akYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tIN3ckYJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Version Control (Team)</tags>
      <menus xmi:id="_tIN3c0YJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tIN3dEYJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tIN3dUYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
      <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_tIN3dkYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tIN3d0YJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_tIN3eEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view search_scope_type=&quot;1&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_tIN3eUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_tIN3ekYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_tIN3e0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3fEYJEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_tIN3fUYJEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3fkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_tIN3hkYJEfC3to_rLoZHFQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_tIPvjkYJEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3i0YJEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_tIN3jEYJEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3jUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_tIN3j0YJEfC3to_rLoZHFQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_tIPtwEYJEfC3to_rLoZHFQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_tIN3kEYJEfC3to_rLoZHFQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_tIPuQUYJEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3kUYJEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_tIN3kkYJEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3tEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3vkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3xUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3xkYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3zEYJEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_tIN3zUYJEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN3zkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_tIN31EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_tIPvR0YJEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN32UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CompilationUnitEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN32kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.GenericEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN320YJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.CompareEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN34UYJEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_tIN34kYJEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN340YJEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_tIN35EYJEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_tIN35UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_tIN36EYJEfC3to_rLoZHFQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_tIN37EYJEfC3to_rLoZHFQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_tIN380YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_tIN39EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_tIN39UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_tIN39kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_tIN3-kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_tIN3-0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_tIN3_EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_tIN3_UYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_tIN3_kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_tIN3_0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <handlers xmi:id="_tIN4AEYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" command="_tIPwQ0YJEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_tIN4AUYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" command="_tIPwREYJEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_tIN4AkYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" command="_tIPwRUYJEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_tIN4A0YJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" command="_tIPwRkYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN4BEYJEfC3to_rLoZHFQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_tIN5yUYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN4BUYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+SPACE" command="_tIPtnkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4BkYJEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+F" command="_tIPtJUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4B0YJEfC3to_rLoZHFQ" keySequence="SHIFT+F10" command="_tIPtfEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4CEYJEfC3to_rLoZHFQ" keySequence="ALT+PAGE_UP" command="_tIPuUEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4CUYJEfC3to_rLoZHFQ" keySequence="ALT+PAGE_DOWN" command="_tIPvEEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4CkYJEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_tIPtA0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4C0YJEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_UP" command="_tIPvp0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4DEYJEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_DOWN" command="_tIPtikYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4DUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F1" command="_tIPtPEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4DkYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F2" command="_tIPu-EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4D0YJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F3" command="_tIPvm0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4EEYJEfC3to_rLoZHFQ" keySequence="COMMAND+X" command="_tIPtxkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4EUYJEfC3to_rLoZHFQ" keySequence="COMMAND+Z" command="_tIPtwEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4EkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Z" command="_tIPuQUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4E0YJEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_tIPtgUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4FEYJEfC3to_rLoZHFQ" keySequence="COMMAND+6" command="_tIPtqUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4FUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+I" command="_tIPtYEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4FkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+L" command="_tIPvxkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4F0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+D" command="_tIPv8EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4GEYJEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_tIPs40YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4GUYJEfC3to_rLoZHFQ" keySequence="COMMAND+A" command="_tIPuFkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4GkYJEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_tIPudkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4G0YJEfC3to_rLoZHFQ" keySequence="ALT+SPACE" command="_tIPvZkYJEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_tIN4HEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_tIN5ykYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN4HUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q B" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4HkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_tIN4H0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q C" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4IEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_tIN4IUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q D" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4IkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_tIN4I0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q O" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4JEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_tIN4JUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q P" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4JkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_tIN4J0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Q" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4KEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q S" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4KUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_tIN4KkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q T" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4K0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_tIN4LEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q V" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4LUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_tIN4LkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q H" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4L0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_tIN4MEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q J" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4MUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_tIN4MkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q K" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4M0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_tIN4NEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q L" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4NUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_tIN4NkYJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+SHIFT+T" command="_tIPtAkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4N0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q X" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4OEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_tIN4OUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Y" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4OkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_tIN4O0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Z" command="_tIPvAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4PEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_tIN4PUYJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+B" command="_tIPvCUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4PkYJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+P" command="_tIPtKUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4P0YJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+T" command="_tIPucUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4QEYJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_tIPs-0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4QUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+A" command="_tIPvHkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4QkYJEfC3to_rLoZHFQ" keySequence="CTRL+Q" command="_tIPvoUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4Q0YJEfC3to_rLoZHFQ" keySequence="CTRL+H" command="_tIPvZUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4REYJEfC3to_rLoZHFQ" keySequence="CTRL+M" command="_tIPvYUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4RUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+P" command="_tIPu-kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4RkYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+H" command="_tIPtr0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4R0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+L" command="_tIPurkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4SEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+M" command="_tIPv5kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4SUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_tIPu4EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4SkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_tIPto0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4S0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F7" command="_tIPv50YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4TEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F8" command="_tIPtoEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4TUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F9" command="_tIPuDEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4TkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F10" command="_tIPuiEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4T0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_LEFT" command="_tIPtB0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4UEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_RIGHT" command="_tIPt2EYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4UUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F11" command="_tIPvtUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4UkYJEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_tIPu0UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4U0YJEfC3to_rLoZHFQ" keySequence="SHIFT+F5" command="_tIPuKEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4VEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F6" command="_tIPu6kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4VUYJEfC3to_rLoZHFQ" keySequence="ALT+F7" command="_tIPuoUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4VkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F12" command="_tIPtOUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4V0YJEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_tIPuD0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4WEYJEfC3to_rLoZHFQ" keySequence="COMMAND+F7" command="_tIPud0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4WUYJEfC3to_rLoZHFQ" keySequence="COMMAND+F8" command="_tIPthEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4WkYJEfC3to_rLoZHFQ" keySequence="COMMAND+F9" command="_tIPtSkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4W0YJEfC3to_rLoZHFQ" keySequence="COMMAND+F11" command="_tIPv4kYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4XEYJEfC3to_rLoZHFQ" keySequence="COMMAND+F12" command="_tIPvaEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4XUYJEfC3to_rLoZHFQ" keySequence="F2" command="_tIPs6EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4XkYJEfC3to_rLoZHFQ" keySequence="F3" command="_tIPtekYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4X0YJEfC3to_rLoZHFQ" keySequence="F4" command="_tIPs8UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4YEYJEfC3to_rLoZHFQ" keySequence="F5" command="_tIPt4UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4YUYJEfC3to_rLoZHFQ" keySequence="COMMAND+F6" command="_tIPtKkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4YkYJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_tIPvoUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4Y0YJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_tIPtQEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4ZEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X O" command="_tIPu0kYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4ZUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X P" command="_tIPv_UYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4ZkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X Q" command="_tIPtXkYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4Z0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X T" command="_tIPuFEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4aEYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X M" command="_tIPuHUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4aUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X B" command="_tIPwOUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4akYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_UP" command="_tIPuzEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4a0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_DOWN" command="_tIPwJ0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4bEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_RIGHT" command="_tIPutkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4bUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F7" command="_tIPvIkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4bkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+F12" command="_tIPv-EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4b0YJEfC3to_rLoZHFQ" keySequence="COMMAND+[" command="_tIPtB0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4cEYJEfC3to_rLoZHFQ" keySequence="COMMAND+]" command="_tIPt2EYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4cUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Z" command="_tIPuZEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4ckYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X R" command="_tIPuQEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4c0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X G" command="_tIPv3kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4dEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X J" command="_tIPvFEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4dUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+[" command="_tIPtoUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4dkYJEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_tIN4d0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X A" command="_tIPs5UYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4eEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X E" command="_tIPu9UYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4eUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+R" command="_tIPwPkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4ekYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+S" command="_tIPu4UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4e0YJEfC3to_rLoZHFQ" keySequence="COMMAND+3" command="_tIPti0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4fEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+C" command="_tIPvakYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4fUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_tIPtxUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4fkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_tIPtzkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4f0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+U" command="_tIPtS0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4gEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_tIPvt0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4gUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F" command="_tIPvpUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4gkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+G" command="_tIPu9kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4g0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+W" command="_tIPtxEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4hEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+H" command="_tIPuakYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4hUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+K" command="_tIPtN0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4hkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+N" command="_tIPuikYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4h0YJEfC3to_rLoZHFQ" keySequence="COMMAND+." command="_tIPwDEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4iEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_tIPv6kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4iUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_tIPvDkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4ikYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+B" command="_tIPtOEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4i0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_tIPuRkYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4jEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_tIPuX0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4jUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+T" command="_tIPumkYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4jkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+E" command="_tIPtRkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4j0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+V" command="_tIPuA0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4kEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_tIPwGUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4kUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+W" command="_tIPwB0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4kkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+I" command="_tIPtBUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4k0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+J" command="_tIPts0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4lEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+K" command="_tIPt_UYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4lUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+L" command="_tIPtfUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4lkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_tIPv90YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4l0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_tIPtwUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4mEYJEfC3to_rLoZHFQ" keySequence="COMMAND+P" command="_tIPvjkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4mUYJEfC3to_rLoZHFQ" keySequence="COMMAND+S" command="_tIPt-kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4mkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B D" command="_tIPv80YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4m0YJEfC3to_rLoZHFQ" keySequence="COMMAND+U" command="_tIPuPEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4nEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B F" command="_tIPtZEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4nUYJEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_tIPuSkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4nkYJEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_tIPvVEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4n0YJEfC3to_rLoZHFQ" keySequence="COMMAND+K" command="_tIPvC0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4oEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+-" command="_tIPtoUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_tIN4oUYJEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_tIN4okYJEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_tIPwHkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4o0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_tIPs5kYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4pEYJEfC3to_rLoZHFQ" keySequence="COMMAND+B" command="_tIPs6UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4pUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B R" command="_tIPuu0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4pkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B S" command="_tIPvUUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4p0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+3" command="_tIPtBEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4qEYJEfC3to_rLoZHFQ" keySequence="COMMAND+E" command="_tIPttUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4qUYJEfC3to_rLoZHFQ" keySequence="COMMAND+F" command="_tIPtHkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4qkYJEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_tIPsuUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4q0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D E" command="_tIPwMEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4rEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D A" command="_tIPvfEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4rUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D T" command="_tIPsxUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4rkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D J" command="_tIPvP0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4r0YJEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_tIPvVEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4sEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D O" command="_tIPue0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4sUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D P" command="_tIPvikYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4skYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D Q" command="_tIPuL0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4s0YJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D B" command="_tIPv7UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4tEYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D R" command="_tIPuQkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4tUYJEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_tIPwuEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4tkYJEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_tIPws0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4t0YJEfC3to_rLoZHFQ" keySequence="COMMAND+BS" command="_tIPtL0YJEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_tIN4uEYJEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_tIOeZEYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN4uUYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+P" command="_tIPvaUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4ukYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+D" command="_tIPuO0YJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN4u0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_tIN5zkYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN4vEYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+Q" command="_tIPtZkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4vUYJEfC3to_rLoZHFQ" keySequence="CTRL+." command="_tIPv1UYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4vkYJEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_tIPtskYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4v0YJEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_MULTIPLY" command="_tIPvNEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4wEYJEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_ADD" command="_tIPv9kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4wUYJEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_SUBTRACT" command="_tIPvmEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4wkYJEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_DIVIDE" command="_tIPtPkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4w0YJEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_tIPur0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4xEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_tIPvOkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4xUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_tIPuwEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4xkYJEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_tIPwK0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4x0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_UP" command="_tIPwCEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4yEYJEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_tIPvHEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4yUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_DOWN" command="_tIPuIkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4ykYJEfC3to_rLoZHFQ" keySequence="ALT+ARROW_LEFT" command="_tIPucEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4y0YJEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_tIPtZUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4zEYJEfC3to_rLoZHFQ" keySequence="SHIFT+END" command="_tIPubkYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4zUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+INSERT" command="_tIPtTEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4zkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_LEFT" command="_tIPt6EYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4z0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_RIGHT" command="_tIPuAUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN40EYJEfC3to_rLoZHFQ" keySequence="SHIFT+HOME" command="_tIPvWkYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN40UYJEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_tIPvlEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN40kYJEfC3to_rLoZHFQ" keySequence="COMMAND+END" command="_tIPvIEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN400YJEfC3to_rLoZHFQ" keySequence="END" command="_tIPvIEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN41EYJEfC3to_rLoZHFQ" keySequence="INSERT" command="_tIPuukYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN41UYJEfC3to_rLoZHFQ" keySequence="F2" command="_tIPtjEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN41kYJEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_tIPv0kYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN410YJEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_RIGHT" command="_tIPvtEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN42EYJEfC3to_rLoZHFQ" keySequence="COMMAND+HOME" command="_tIPs4kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN42UYJEfC3to_rLoZHFQ" keySequence="HOME" command="_tIPs4kYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN42kYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_tIPuBEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN420YJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_tIPtU0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN43EYJEfC3to_rLoZHFQ" keySequence="ALT+DEL" command="_tIPtuEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN43UYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+DEL" command="_tIPva0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN43kYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Y" command="_tIPsr0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN430YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+X" command="_tIPuf0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN44EYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Y" command="_tIPt9UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN44UYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_tIPur0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN44kYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+A" command="_tIPul0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN440YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+J" command="_tIPtW0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN45EYJEfC3to_rLoZHFQ" keySequence="COMMAND++" command="_tIPu7UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN45UYJEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_tIPt-EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN45kYJEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_tIPsw0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN450YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_tIPsykYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN46EYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_tIPsw0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN46UYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_tIPtqEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN46kYJEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_tIPwBkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN460YJEfC3to_rLoZHFQ" keySequence="COMMAND+J" command="_tIPtCkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN47EYJEfC3to_rLoZHFQ" keySequence="COMMAND+L" command="_tIPvekYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN47UYJEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_tIPv5EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN47kYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_tIPtskYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN470YJEfC3to_rLoZHFQ" keySequence="COMMAND+D" command="_tIPtFkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN48EYJEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_tIPu7UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN48UYJEfC3to_rLoZHFQ" keySequence="SHIFT+CR" command="_tIPv0UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN48kYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+CR" command="_tIPvmkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN480YJEfC3to_rLoZHFQ" keySequence="ALT+BS" command="_tIPsuEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_tIN49EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_tIN500YJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN49UYJEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_tIPuPUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN49kYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_tIPuRkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN490YJEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_tIPtF0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4-EYJEfC3to_rLoZHFQ" keySequence="COMMAND+F3" command="_tIPwCkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN4-UYJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_UP" command="_tIPuTUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4-kYJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_tIPuG0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4-0YJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+END" command="_tIPtEkYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4_EYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_UP" command="_tIPuLkYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4_UYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_DOWN" command="_tIPtO0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4_kYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+HOME" command="_tIPt30YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN4_0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_tIPtF0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5AEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_tIPvIUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5AUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_tIPwL0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5AkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_tIPtxUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5A0YJEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_tIPujUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5BEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_tIPtjUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5BUYJEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_tIPujUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5BkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_tIPujUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5B0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+U" command="_tIPvRkYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5CEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_tIPv0EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5CUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_tIPtu0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5CkYJEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_tIPulkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5C0YJEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_tIPt1kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5DEYJEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_tIPuF0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5DUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+'" command="_tIPuqEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5DkYJEfC3to_rLoZHFQ" keySequence="COMMAND+2 F" command="_tIPv9UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5D0YJEfC3to_rLoZHFQ" keySequence="COMMAND+2 R" command="_tIPvVkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5EEYJEfC3to_rLoZHFQ" keySequence="COMMAND+2 T" command="_tIPujEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5EUYJEfC3to_rLoZHFQ" keySequence="COMMAND+2 L" command="_tIPtC0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5EkYJEfC3to_rLoZHFQ" keySequence="COMMAND+2 M" command="_tIPt5EYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5E0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_tIN50UYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5FEYJEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_tIPvcUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5FUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_tIPs6EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5FkYJEfC3to_rLoZHFQ" keySequence="F3" command="_tIPvokYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5F0YJEfC3to_rLoZHFQ" keySequence="F4" command="_tIPstUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5GEYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_tIPum0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5GUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_tIPun0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5GkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_tIPt70YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5G0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_tIPvs0YJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5HEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_tIN56EYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5HUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_tIPuXUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5HkYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_tIPuAkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5H0YJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_tIPvK0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5IEYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_tIPt-UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5IUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+N" command="_tIPtLEYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5IkYJEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_tIPu_kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5I0YJEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_tIPtsUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5JEYJEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_tIPtLEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5JUYJEfC3to_rLoZHFQ" keySequence="INSERT" command="_tIPt8kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5JkYJEfC3to_rLoZHFQ" keySequence="F4" command="_tIPtAEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5J0YJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_tIPvWEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5KEYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_tIPt_kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5KUYJEfC3to_rLoZHFQ" keySequence="ALT+N" command="_tIPt8kYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5KkYJEfC3to_rLoZHFQ" keySequence="COMMAND+CR" command="_tIPtrUYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5K0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_tIN51EYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5LEYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_tIPuXUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5LUYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_tIPuAkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5LkYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+S" command="_tIPty0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5L0YJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_tIPvK0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5MEYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_tIPt-UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5MUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_tIPs1EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5MkYJEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_tIPwAEYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5M0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" bindingContext="_tIN53UYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5NEYJEfC3to_rLoZHFQ" keySequence="CTRL+D" command="_tIPwA0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5NUYJEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_tIPuo0YJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5NkYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_tIN5zEYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5N0YJEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_tIPst0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5OEYJEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_tIPv4UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5OUYJEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_tIPt7UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5OkYJEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_tIPvS0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5O0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_tIPt7UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5PEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_tIPvS0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5PUYJEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_tIPt7UYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5PkYJEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_tIPvS0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_tIN5P0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_tIN520YJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5QEYJEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_tIPuqkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5QUYJEfC3to_rLoZHFQ" keySequence="F3" command="_tIPsyEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5QkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_tIPs8EYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5Q0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_tIPv0EYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5REYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_tIPswUYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_tIN5RUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_tIN540YJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5RkYJEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_tIPv3UYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5R0YJEfC3to_rLoZHFQ" keySequence="F7" command="_tIPwEEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5SEYJEfC3to_rLoZHFQ" keySequence="F8" command="_tIPutEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5SUYJEfC3to_rLoZHFQ" keySequence="COMMAND+F2" command="_tIPvbkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5SkYJEfC3to_rLoZHFQ" keySequence="F5" command="_tIPs9kYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5S0YJEfC3to_rLoZHFQ" keySequence="F6" command="_tIPuBUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5TEYJEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_tIPuekYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5TUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_tIN54EYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5TkYJEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_tIPu20YJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5T0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_tIN51UYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5UEYJEfC3to_rLoZHFQ" keySequence="F1" command="_tIPsxkYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5UUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_tIN560YJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5UkYJEfC3to_rLoZHFQ" keySequence="F2" command="_tIPtMkYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5U0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_tIN52EYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5VEYJEfC3to_rLoZHFQ" keySequence="F3" command="_tIPulUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5VUYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_tIPs-UYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5VkYJEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_tIPu1EYJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5V0YJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_tIPvF0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5WEYJEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_tIPvLUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5WUYJEfC3to_rLoZHFQ" keySequence="COMMAND+\" command="_tIPu90YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5WkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_tIPu90YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5W0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_tIPumEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5XEYJEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_tIPuh0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_tIN5XUYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_tIPwO0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5XkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_tIPvzkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5X0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_tIPwGkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5YEYJEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_tIPvk0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5YUYJEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_tIPup0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5YkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_tIPuh0YJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5Y0YJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_tIN56UYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5ZEYJEfC3to_rLoZHFQ" keySequence="F5" command="_tIPvzUYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5ZUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_tIN56kYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5ZkYJEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_tIPtE0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5Z0YJEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_tIPtlUYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5aEYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_tIN550YJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5aUYJEfC3to_rLoZHFQ" keySequence="ALT+Y" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5akYJEfC3to_rLoZHFQ" keySequence="ALT+A" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5a0YJEfC3to_rLoZHFQ" keySequence="ALT+B" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5bEYJEfC3to_rLoZHFQ" keySequence="ALT+C" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5bUYJEfC3to_rLoZHFQ" keySequence="ALT+D" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5bkYJEfC3to_rLoZHFQ" keySequence="ALT+E" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5b0YJEfC3to_rLoZHFQ" keySequence="ALT+F" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5cEYJEfC3to_rLoZHFQ" keySequence="ALT+G" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5cUYJEfC3to_rLoZHFQ" keySequence="ALT+P" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5ckYJEfC3to_rLoZHFQ" keySequence="ALT+R" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5c0YJEfC3to_rLoZHFQ" keySequence="ALT+S" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5dEYJEfC3to_rLoZHFQ" keySequence="ALT+T" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5dUYJEfC3to_rLoZHFQ" keySequence="ALT+V" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5dkYJEfC3to_rLoZHFQ" keySequence="ALT+W" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5d0YJEfC3to_rLoZHFQ" keySequence="ALT+H" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5eEYJEfC3to_rLoZHFQ" keySequence="ALT+L" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5eUYJEfC3to_rLoZHFQ" keySequence="ALT+N" command="_tIPuhUYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5ekYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_tIN50kYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5e0YJEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_tIPwFkYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5fEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_tIOeY0YJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5fUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_tIPwL0YJEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_tIN5fkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="_tIN54kYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5f0YJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+D" command="_tIPvD0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5gEYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+P" command="_tIPvJ0YJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5gUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_tIPwDUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5gkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_tIPtckYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5g0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_tIN53EYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5hEYJEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_tIPujUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5hUYJEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_tIPujUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5hkYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_tIPujUYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5h0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_tIN510YJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5iEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_tIPs3UYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5iUYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_tIN50EYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5ikYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_tIPtEEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5i0YJEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_tIPtJEYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5jEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_tIN54UYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5jUYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_tIPuEUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5jkYJEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_tIPv-UYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5j0YJEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_tIPtkEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5kEYJEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_tIPuxUYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5kUYJEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_tIPu8EYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5kkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_tIN55EYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5k0YJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+," command="_tIPvpEYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5lEYJEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_tIPvXkYJEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_tIN5lUYJEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_tIPvX0YJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5lkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_tIN5z0YJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5l0YJEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_tIPuLUYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5mEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_tIN51kYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5mUYJEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_tIPs3UYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5mkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_tIN55kYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5m0YJEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_tIPtK0YJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5nEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_tIN5zUYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5nUYJEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_tIPuaUYJEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_tIN5nkYJEfC3to_rLoZHFQ" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" bindingContext="_tIOeYEYJEfC3to_rLoZHFQ">
    <bindings xmi:id="_tIN5n0YJEfC3to_rLoZHFQ" keySequence="M1+W" command="_tIPwRkYJEfC3to_rLoZHFQ">
      <tags>deleted</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_tIN5oEYJEfC3to_rLoZHFQ" bindingContext="_tIOeZUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5oUYJEfC3to_rLoZHFQ" bindingContext="_tIOeZkYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5okYJEfC3to_rLoZHFQ" bindingContext="_tIOeZ0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5o0YJEfC3to_rLoZHFQ" bindingContext="_tIOeaEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5pEYJEfC3to_rLoZHFQ" bindingContext="_tIOeaUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5pUYJEfC3to_rLoZHFQ" bindingContext="_tIOeakYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5pkYJEfC3to_rLoZHFQ" bindingContext="_tIOea0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5p0YJEfC3to_rLoZHFQ" bindingContext="_tIOebEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5qEYJEfC3to_rLoZHFQ" bindingContext="_tIOebUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5qUYJEfC3to_rLoZHFQ" bindingContext="_tIOebkYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5qkYJEfC3to_rLoZHFQ" bindingContext="_tIOeb0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5q0YJEfC3to_rLoZHFQ" bindingContext="_tIOecEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5rEYJEfC3to_rLoZHFQ" bindingContext="_tIOecUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5rUYJEfC3to_rLoZHFQ" bindingContext="_tIOeckYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5rkYJEfC3to_rLoZHFQ" bindingContext="_tIOec0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5r0YJEfC3to_rLoZHFQ" bindingContext="_tIOedEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5sEYJEfC3to_rLoZHFQ" bindingContext="_tIOedUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5sUYJEfC3to_rLoZHFQ" bindingContext="_tIOedkYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5skYJEfC3to_rLoZHFQ" bindingContext="_tIOed0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5s0YJEfC3to_rLoZHFQ" bindingContext="_tIOeeEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5tEYJEfC3to_rLoZHFQ" bindingContext="_tIOeeUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5tUYJEfC3to_rLoZHFQ" bindingContext="_tIOeekYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5tkYJEfC3to_rLoZHFQ" bindingContext="_tIOee0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5t0YJEfC3to_rLoZHFQ" bindingContext="_tIOefEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5uEYJEfC3to_rLoZHFQ" bindingContext="_tIOefUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5uUYJEfC3to_rLoZHFQ" bindingContext="_tIOefkYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5ukYJEfC3to_rLoZHFQ" bindingContext="_tIOef0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5u0YJEfC3to_rLoZHFQ" bindingContext="_tIOegEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5vEYJEfC3to_rLoZHFQ" bindingContext="_tIOegUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5vUYJEfC3to_rLoZHFQ" bindingContext="_tIOegkYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5vkYJEfC3to_rLoZHFQ" bindingContext="_tIOeg0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5v0YJEfC3to_rLoZHFQ" bindingContext="_tIOehEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5wEYJEfC3to_rLoZHFQ" bindingContext="_tIOehUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5wUYJEfC3to_rLoZHFQ" bindingContext="_tIOehkYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5wkYJEfC3to_rLoZHFQ" bindingContext="_tIOeh0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5w0YJEfC3to_rLoZHFQ" bindingContext="_tIOeiEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5xEYJEfC3to_rLoZHFQ" bindingContext="_tIOeiUYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5xUYJEfC3to_rLoZHFQ" bindingContext="_tIOeikYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5xkYJEfC3to_rLoZHFQ" bindingContext="_tIOei0YJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5x0YJEfC3to_rLoZHFQ" bindingContext="_tIOejEYJEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_tIN5yEYJEfC3to_rLoZHFQ" bindingContext="_tIOejUYJEfC3to_rLoZHFQ"/>
  <rootContext xmi:id="_tIN5yUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_tIN5ykYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_tIN5y0YJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_tIN5zEYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_tIN5zUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_tIN5zkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_tIN5z0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_tIN50EYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_tIN50UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_tIN50kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_tIN500YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_tIN51EYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_tIN51UYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_tIN51kYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_tIN510YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_tIN52EYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_tIN52UYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_tIN52kYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="_tIN520YJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_tIN53EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_tIN53UYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_tIN53kYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_tIN530YJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_tIN54EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_tIN54UYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_tIN54kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_tIN540YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_tIN55EYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_tIN55UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_tIN55kYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_tIN550YJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_tIN56EYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_tIN56UYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_tIN56kYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_tIN560YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_tIOeYEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_tIOeYUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_tIOeYkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_tIOeY0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_tIOeZEYJEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_tIOeZUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_tIOeZkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_tIOeZ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_tIOeaEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_tIOeaUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_tIOeakYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_tIOea0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_tIOebEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_tIOebUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_tIOebkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_tIOeb0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_tIOecEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_tIOecUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_tIOeckYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_tIOec0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_tIOedEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_tIOedUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_tIOedkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_tIOed0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_tIOeeEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_tIOeeUYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_tIOeekYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_tIOee0YJEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_tIOefEYJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_tIOefUYJEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_tIOefkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_tIOef0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_tIOegEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_tIOegUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_tIOegkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_tIOeg0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_tIOehEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_tIOehUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_tIOehkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_tIOeh0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_tIOeiEYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_tIOeiUYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_tIOeikYJEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <rootContext xmi:id="_tIOei0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_tIOejEYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_tIOejUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_tIOejkYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_tIOej0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_tIOekEYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_tIOekUYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_tIOekkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOek0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOelEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOelUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOelkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOel0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOemEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOemUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOemkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_tIOem0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_tIOenEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_tIOenUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_tIOenkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_tIOen0YJEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeoEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeoUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeokYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeo0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_tIOepEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_tIOepUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_tIOepkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_tIOep0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeqEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeqUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeqkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeq0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOerEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_tIOerUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_tIOerkYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_tIOer0YJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_tIOesEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_tIOesUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeskYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_tIOes0YJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_tIOetEYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_tIOetUYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_tIOetkYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_tIOet0YJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeuEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeuUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeukYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeu0YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_tIOevEYJEfC3to_rLoZHFQ" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_tIOevUYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_tIOevkYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_tIOev0YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_tIOewEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_tIOewUYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_tIOewkYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_tIOew0YJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOexEYJEfC3to_rLoZHFQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_tIOexUYJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_tIOexkYJEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_tIOex0YJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeyEYJEfC3to_rLoZHFQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeyUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOeykYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOey0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_tIOezEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOezUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOezkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOez0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe0EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe0UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe0kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe00YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe1EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe1UYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe1kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe10YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe2EYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe2UYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe2kYJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe20YJEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe3EYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe3UYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_tIOe3kYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" category="JRebel" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
    <tags>View</tags>
    <tags>inject</tags>
    <tags>categoryTag:JRebel</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <trimContributions xmi:id="_tIPsn0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_tIPsoEYJEfC3to_rLoZHFQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_tIPsoUYJEfC3to_rLoZHFQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_tIPsokYJEfC3to_rLoZHFQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_tIPsrUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsrkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsr0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPssEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPssUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_tIPsskYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPss0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPstEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPstUYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPstkYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPst0YJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_tIPw5EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsuEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsuUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsukYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsu0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsvEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsvUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsvkYJEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPsv0YJEfC3to_rLoZHFQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_tIPswEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPswUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPswkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsw0YJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="_tIPw4EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsxEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsxUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsxkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsx0YJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_tIPw6UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsyEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsyUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsykYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_tIPwykYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsy0YJEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPszEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPszUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPszkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPsz0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs0EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs0UYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs0kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs00YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs1EYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_tIPwzkYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs1UYJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs1kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs10YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs2EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_tIPw1EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPs2UYJEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_tIPs2kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs20YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs3EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs3UYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs3kYJEfC3to_rLoZHFQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_tIPw1UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs30YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs4EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs4UYJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs4kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs40YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs5EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs5UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs5kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs50YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs6EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs6UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs6kYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs60YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs7EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs7UYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs7kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs70YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs8EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs8UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs8kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs80YJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_tIPw6kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs9EYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs9UYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs9kYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs90YJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs-EYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs-UYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs-kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs-0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs_EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs_UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPs_kYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPs_0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_tIPtAEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtAUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtAkYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_tIPw2kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtA0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtBEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtBUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtBkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtB0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtCEYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtCUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtCkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtC0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtDEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtDUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtDkYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtD0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtEEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_tIPw7kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtEUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtEkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtE0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtFEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtFUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtFkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtF0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtGEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtGUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtGkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtG0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_tIPw1EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtHEYJEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_tIPtHUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtHkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtH0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtIEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtIUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtIkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_tIPtI0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtJEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtJUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtJkYJEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtJ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_tIPwx0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtKEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtKUYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtKkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtK0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtLEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtLUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtLkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtL0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtMEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtMUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtMkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtM0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtNEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtNUYJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtNkYJEfC3to_rLoZHFQ" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_tIPtN0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtOEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtOUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtOkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtO0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtPEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_tIPw7UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtPUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtPkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtP0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtQEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtQUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtQkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtQ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_tIPw2UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtREYJEfC3to_rLoZHFQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_tIPtRUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtRkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtR0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtSEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtSUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtSkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtS0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtTEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtTUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtTkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtT0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtUEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtUUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtUkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtU0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtVEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_tIPw6UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtVUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtVkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtV0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtWEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtWUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtWkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtW0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtXEYJEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtXUYJEfC3to_rLoZHFQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_tIPtXkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtX0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtYEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtYUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtYkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtY0YJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtZEYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtZUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtZkYJEfC3to_rLoZHFQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtZ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtaEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtaUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtakYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPta0YJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtbEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtbUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtbkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtb0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtcEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_tIPw10YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtcUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtckYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_tIPw1kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtc0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_tIPw30YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtdEYJEfC3to_rLoZHFQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_tIPtdUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtdkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtd0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPteEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPteUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtekYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPte0YJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtfEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtfUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtfkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtf0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtgEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtgUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtgkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtg0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPthEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPthUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPthkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPth0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtiEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_tIPw6EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtiUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_tIPtikYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPti0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtjEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtjUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtjkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtj0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_tIPwzkYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtkEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtkUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtkkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtk0YJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtlEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtlUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtlkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtl0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_tIPw3UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtmEYJEfC3to_rLoZHFQ" elementId="url" name="URL"/>
    <parameters xmi:id="_tIPtmUYJEfC3to_rLoZHFQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_tIPtmkYJEfC3to_rLoZHFQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_tIPtm0YJEfC3to_rLoZHFQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_tIPtnEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtnUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtnkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtn0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtoEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtoUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_tIPw3UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPtokYJEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_tIPto0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtpEYJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtpUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtpkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtp0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtqEYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtqUYJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="_tIPwyUYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtqkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtq0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtrEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtrUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtrkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtr0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtsEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtsUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtskYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_tIPwykYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPts0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPttEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPttUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPttkYJEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_tIPw1UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtt0YJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtuEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtuUYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="_tIPwzEYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtukYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtu0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtvEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtvUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtvkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtv0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtwEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtwUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtwkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtw0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtxEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtxUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtxkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtx0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtyEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtyUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtykYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPty0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_tIPwzkYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtzEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtzUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtzkYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPtz0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt0EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt0UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_tIPw2UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPt0kYJEfC3to_rLoZHFQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_tIPt00YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt1EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt1UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_tIPw6UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt1kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt10YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt2EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt2UYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt2kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt20YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt3EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt3UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt3kYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt30YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt4EYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt4UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt4kYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt40YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt5EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt5UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt5kYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt50YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt6EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt6UYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt6kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt60YJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="_tIPw4EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt7EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt7UYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_tIPw5EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt7kYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt70YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt8EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_tIPw1EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPt8UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_tIPt8kYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt80YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt9EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt9UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt9kYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt90YJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt-EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt-UYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt-kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt-0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt_EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt_UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt_kYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPt_0YJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_tIPwx0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuAEYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuAUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuAkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuA0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuBEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuBUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuBkYJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuB0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_tIPw2UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuCEYJEfC3to_rLoZHFQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_tIPuCUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuCkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuC0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuDEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuDUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuDkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuD0YJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuEEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuEUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuEkYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuE0YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuFEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuFUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuFkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuF0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuGEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_tIPw6kYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuGUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_tIPuGkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_tIPuG0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuHEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuHUYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuHkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuH0YJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuIEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuIUYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuIkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuI0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuJEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuJUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuJkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuJ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuKEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuKUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuKkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuK0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuLEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_tIPw7EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuLUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuLkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuL0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuMEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuMUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_tIPw3UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuMkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_tIPuM0YJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuNEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuNUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_tIPw30YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuNkYJEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_tIPuN0YJEfC3to_rLoZHFQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_tIPuOEYJEfC3to_rLoZHFQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_tIPuOUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuOkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuO0YJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuPEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuPUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuPkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuP0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuQEYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuQUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuQkYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuQ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuREYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuRUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuRkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuR0YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuSEYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_tIPw2kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuSUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuSkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuS0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuTEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuTUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuTkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuT0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_tIPuUEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuUUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuUkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_tIPw1EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuU0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_tIPuVEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuVUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuVkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_tIPw50YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuV0YJEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_tIPuWEYJEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_tIPuWUYJEfC3to_rLoZHFQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_tIPuWkYJEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_tIPuW0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuXEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuXUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuXkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuX0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuYEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuYUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuYkYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuY0YJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuZEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuZUYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuZkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuZ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuaEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuaUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuakYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPua0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPubEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPubUYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_tIPwz0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPubkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPub0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPucEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPucUYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_tIPw2kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuckYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_tIPw3UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuc0YJEfC3to_rLoZHFQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_tIPudEYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPudUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPudkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPud0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPueEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPueUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuekYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPue0YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPufEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPufUYJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="_tIPw4EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPufkYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuf0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPugEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPugUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPugkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPug0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuhEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuhUYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_tIPw5EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuhkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuh0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuiEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuiUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuikYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPui0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPujEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPujUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPujkYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuj0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPukEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPukUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPukkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuk0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPulEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPulUYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPulkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPul0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPumEYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPumUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPumkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPum0YJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPunEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPunUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPunkYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="_tIPwzEYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPun0YJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuoEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuoUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuokYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuo0YJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPupEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_tIPw6EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPupUYJEfC3to_rLoZHFQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_tIPupkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPup0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuqEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuqUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuqkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuq0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPurEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPurUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPurkYJEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_tIPw3EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPur0YJEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_tIPwykYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPusEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPusUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuskYJEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPus0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPutEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPutUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPutkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPut0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuuEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuuUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuukYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuu0YJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuvEYJEfC3to_rLoZHFQ" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuvUYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_tIPuvkYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_tIPuv0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuwEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuwUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuwkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuw0YJEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPuxEYJEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_tIPuxUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuxkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPux0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuyEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuyUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuykYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuy0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuzEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuzUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuzkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPuz0YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu0EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu0UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu0kYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu00YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu1EYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu1UYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu1kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu10YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu2EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu2UYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_tIPw6UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu2kYJEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu20YJEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_tIPw1UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu3EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu3UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_tIPwy0YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPu3kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_tIPu30YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu4EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu4UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu4kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu40YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu5EYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="_tIPwzEYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu5UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu5kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu50YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu6EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu6UYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu6kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu60YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu7EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu7UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu7kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu70YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu8EYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu8UYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu8kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu80YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu9EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu9UYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu9kYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu90YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu-EYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_tIPw7UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu-UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu-kYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu-0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu_EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu_UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu_kYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPu_0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvAEYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="_tIPwzEYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvAUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_tIPwyEYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvAkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_tIPvA0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_tIPvBEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_tIPvBUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvBkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvB0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_tIPw1EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvCEYJEfC3to_rLoZHFQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_tIPvCUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvCkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvC0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvDEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvDUYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="_tIPwzEYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvDkYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvD0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_tIPw1kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvEEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvEUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvEkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_tIPw00YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvE0YJEfC3to_rLoZHFQ" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_tIPvFEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvFUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvFkYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_tIPw7kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvF0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvGEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_tIPw30YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvGUYJEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_tIPvGkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvG0YJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvHEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvHUYJEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvHkYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvH0YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_tIPvIEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvIUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvIkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvI0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvJEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvJUYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvJkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvJ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_tIPw1kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvKEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvKUYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvKkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvK0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvLEYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvLUYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvLkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvL0YJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_tIPw2kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvMEYJEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="_tIPw4kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvMUYJEfC3to_rLoZHFQ" elementId="java.execute.workspaceCommand" commandName="Execute Java Command in Workspace" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvMkYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_tIPvM0YJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_tIPvNEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvNUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvNkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvN0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvOEYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvOUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvOkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvO0YJEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvPEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvPUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvPkYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvP0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvQEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvQUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvQkYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvQ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvREYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvRUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvRkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvR0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvSEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvSUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvSkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvS0YJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_tIPw5EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvTEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvTUYJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvTkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_tIPw6kYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvT0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_tIPvUEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import from a Source Repository" description="Imports a plug-in from a source repository" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvUUYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvUkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvU0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvVEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvVUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvVkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvV0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvWEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvWUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvWkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvW0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvXEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvXUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvXkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvX0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvYEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvYUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvYkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvY0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvZEYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvZUYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvZkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvZ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvaEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvaUYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvakYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPva0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvbEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvbUYJEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="_tIPw4kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvbkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvb0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvcEYJEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvcUYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvckYJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvc0YJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvdEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvdUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvdkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvd0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_tIPw6UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPveEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_tIPveUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_tIPvekYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPve0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvfEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvfUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvfkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvf0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvgEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvgUYJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvgkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvg0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvhEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvhUYJEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvhkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvh0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPviEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPviUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvikYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvi0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvjEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvjUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_tIPw6kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvjkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvj0YJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvkEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvkUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvkkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvk0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvlEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvlUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvlkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvl0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvmEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvmUYJEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvmkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvm0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvnEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvnUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvnkYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvn0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvoEYJEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvoUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvokYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvo0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvpEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvpUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvpkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvp0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvqEYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvqUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_tIPw50YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvqkYJEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_tIPvq0YJEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_tIPvrEYJEfC3to_rLoZHFQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_tIPvrUYJEfC3to_rLoZHFQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_tIPvrkYJEfC3to_rLoZHFQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_tIPvr0YJEfC3to_rLoZHFQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_tIPvsEYJEfC3to_rLoZHFQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_tIPvsUYJEfC3to_rLoZHFQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_tIPvskYJEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_tIPvs0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvtEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvtUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvtkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvt0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvuEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvuUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvukYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvu0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvvEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvvUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvvkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_tIPw6UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvv0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvwEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvwUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvwkYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvw0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvxEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_tIPvxUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_tIPvxkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvx0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvyEYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvyUYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvykYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_tIPw1EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPvy0YJEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_tIPvzEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvzUYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_tIPwyEYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvzkYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPvz0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv0EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv0UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv0kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv00YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv1EYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv1UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv1kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv10YJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_tIPwx0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv2EYJEfC3to_rLoZHFQ" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="_tIPv2UYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="_tIPv2kYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="_tIPv20YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_tIPw4UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv3EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv3UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv3kYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv30YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv4EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv4UYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_tIPw5EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv4kYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv40YJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv5EYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv5UYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv5kYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv50YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv6EYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv6UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv6kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv60YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv7EYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv7UYJEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv7kYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv70YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv8EYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv8UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv8kYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_tIPw2kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv80YJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv9EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv9UYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv9kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv90YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv-EYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv-UYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv-kYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv-0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv_EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv_UYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv_kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPv_0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwAEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_tIPw00YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwAUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwAkYJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_tIPwz0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwA0YJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwBEYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwBUYJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwBkYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_tIPw2EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwB0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwCEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwCUYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwCkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwC0YJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwDEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwDUYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_tIPw1kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwDkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPwD0YJEfC3to_rLoZHFQ" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_tIPwEEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwEUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_tIPw3UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPwEkYJEfC3to_rLoZHFQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_tIPwE0YJEfC3to_rLoZHFQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_tIPwFEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_tIPw30YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwFUYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_tIPw2UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwFkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwF0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwGEYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwGUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwGkYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwG0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwHEYJEfC3to_rLoZHFQ" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwHUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwHkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_tIPw2UYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPwH0YJEfC3to_rLoZHFQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_tIPwIEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwIUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwIkYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwI0YJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwJEYJEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwJUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwJkYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwJ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_tIPw0kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwKEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwKUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwKkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_tIPw5UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwK0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwLEYJEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPwLUYJEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_tIPwLkYJEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_tIPwL0YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_tIPw1EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwMEYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwMUYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_tIPw3kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwMkYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_tIPw60YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwM0YJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_tIPw20YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwNEYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwNUYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="_tIPw6EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwNkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwN0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwOEYJEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwOUYJEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="_tIPw5kYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwOkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_tIPw0UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwO0YJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_tIPwy0YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwPEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwPUYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwPkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_tIPw1EYJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPwP0YJEfC3to_rLoZHFQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_tIPwQEYJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="_tIPw70YJEfC3to_rLoZHFQ">
    <parameters xmi:id="_tIPwQUYJEfC3to_rLoZHFQ" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="_tIPwQkYJEfC3to_rLoZHFQ" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="_tIPwQ0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.arrangeWindowsInFront" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.arrangeWindows.name" description="%command.arrangeWindows.desc" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwREYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.minimizeWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.minimize.name" description="%command.minimize.desc" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwRUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.zoomWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.zoom.name" description="%command.zoom.desc" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwRkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.closeDialog" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.closeDialog.name" description="%command.closeDialog.desc" category="_tIPw3UYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwR0YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_tIPwSEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwSUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwSkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwS0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwTEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwTUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwTkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwT0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwUEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwUUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwUkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwU0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwVEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwVUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwVkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwV0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwWEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwWUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwWkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwW0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwXEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwXUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwXkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwX0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwYEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwYUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwYkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwY0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwZEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwZUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwZkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwZ0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwaEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwaUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwakYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwa0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwbEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwbUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwbkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwb0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwcEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwcUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwckYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwc0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwdEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwdUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwdkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwd0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPweEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPweUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwekYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwe0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwfEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwfUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwfkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwf0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwgEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwgUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwgkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwg0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwhEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwhUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwhkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwh0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwiEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwiUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwikYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwi0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwjEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwjUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwjkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwj0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwkEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwkUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwkkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwk0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwlEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwlUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwlkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwl0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwmEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwmUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwmkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwm0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwnEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwnUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwnkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwn0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwoEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwoUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwokYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwo0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwpEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwpUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwpkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwp0YJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwqEYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwqUYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwqkYJEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwq0YJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelStartup" commandName="JRebel Configuration Startup Page" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwrEYJEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_tIPwyEYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwrUYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebel" commandName="JRebel Configuration" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwrkYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelProjects" commandName="JRebel Configuration Projects Page" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwr0YJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.openSetupGuide" commandName="JRebel Setup Guide" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwsEYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebel" commandName="Activate JRebel" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwsUYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwskYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelRemoteServers" commandName="JRebel Configuration Remote Servers Page" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPws0YJEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_tIPw8EYJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwtEYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.jrebelSupportPopup" commandName="JRebel Support" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwtUYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelAdvanced" commandName="JRebel Configuration Advanced Page" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwtkYJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.remoting.synchronize" commandName="Synchronize" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwt0YJEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebelPopup" commandName="Activate JRebel" category="_tIPw70YJEfC3to_rLoZHFQ"/>
  <commands xmi:id="_tIPwuEYJEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_tIPw8EYJEfC3to_rLoZHFQ"/>
  <addons xmi:id="_tIPwuUYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_tIPwukYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_tIPwu0YJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_tIPwvEYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_tIPwvUYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_tIPwvkYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_tIPwv0YJEfC3to_rLoZHFQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_tIPwwEYJEfC3to_rLoZHFQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_tIPwwUYJEfC3to_rLoZHFQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_tIPwwkYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_tIPww0YJEfC3to_rLoZHFQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_tIPwxEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_tIPwxUYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_tIPwxkYJEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler"/>
  <categories xmi:id="_tIPwx0YJEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_tIPwyEYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_tIPwyUYJEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="_tIPwykYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_tIPwy0YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_tIPwzEYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="_tIPwzUYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_tIPwzkYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_tIPwz0YJEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_tIPw0EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_tIPw0UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_tIPw0kYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_tIPw00YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_tIPw1EYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_tIPw1UYJEfC3to_rLoZHFQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_tIPw1kYJEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_tIPw10YJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_tIPw2EYJEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_tIPw2UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_tIPw2kYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_tIPw20YJEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_tIPw3EYJEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_tIPw3UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_tIPw3kYJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_tIPw30YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_tIPw4EYJEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="_tIPw4UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_tIPw4kYJEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="_tIPw40YJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_tIPw5EYJEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_tIPw5UYJEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_tIPw5kYJEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_tIPw50YJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_tIPw6EYJEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_tIPw6UYJEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_tIPw6kYJEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_tIPw60YJEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_tIPw7EYJEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_tIPw7UYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_tIPw7kYJEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_tIPw70YJEfC3to_rLoZHFQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_tIPw8EYJEfC3to_rLoZHFQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
</application:Application>
