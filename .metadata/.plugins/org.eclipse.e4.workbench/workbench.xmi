<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_iZAzsEIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_iZAzsUIZEfC3to_rLoZHFQ" bindingContexts="_iZCD6UIZEfC3to_rLoZHFQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workbench>&#xA;&lt;mruList>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductEntity.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/model/ProductEntity.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;managed-schema.xml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/data/conf/managed-schema.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SearchUtils.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/util/SearchUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AdvancedSolrFacetingService.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/AdvancedSolrFacetingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImplv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImplv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrToExcel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrToExcel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ResultType.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/enums/ResultType.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrCoreSwapper.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrCoreSwapper.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/IndexerService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;IndexerController.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/controller/IndexerController.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SpringApplication.class&quot; tooltip=&quot;org.springframework.boot.SpringApplication&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/boot\/spring-boot\/3.2.5\/spring-boot-3.2.5.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework.boot=/=/maven.artifactId=/spring-boot=/=/maven.version=/3.2.5=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.boot(SpringApplication.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpApplication.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/MavpApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportListPDFDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/ExportListPDFDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ExportPDFServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/ExportPDFServiceImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;market-indexer/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepositoryImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepositoryImpl.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentFacetingTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/data/solr/repository/SolrNestedDocumentFacetingTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepository.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/BeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrNestedDocumentCodebaseVerification.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/verification/SolrNestedDocumentCodebaseVerification.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;mavp-backend/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ChildOfferModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/ChildOfferModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/OfferJsonModelDeserializerTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerFactory.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/OfferJsonModelDeserializerFactory.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotPricesDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/DepotPricesDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializerModifier.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializerModifier.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModelDeserializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModelDeserializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferJsonModel2.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/OfferJsonModel2.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotsReadyEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/DepotsReadyEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application-dev.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-dev.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-dev.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;categories.txt&quot; tooltip=&quot;mavp-backend/src/main/resources/category/categories.txt&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/category/categories.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrClient.class&quot; tooltip=&quot;org.apache.solr.client.solrj.SolrClient&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.apache.solr.client.solrj(SolrClient.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootPropertyEditor&quot; name=&quot;application.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrIndexer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrIndexer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MarketIndexerApplication.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/MarketIndexerApplication.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/SolrBeanConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotInfo.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/model/DepotInfo.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AlternativeSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/AlternativeSearchDto.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-test.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-test.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-test.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;NestedDocumentVerificationTest.java&quot; tooltip=&quot;mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/test/java/tr/gov/tubitak/mavp/NestedDocumentVerificationTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-temp.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-temp.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-temp.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrProductModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;SolrInputDocument.class&quot; tooltip=&quot;org.apache.solr.common.SolrInputDocument&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common(SolrInputDocument.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrTemplate.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/solr/SolrTemplate.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CommonParams.class&quot; tooltip=&quot;org.apache.solr.common.params.CommonParams&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar&amp;lt;org.apache.solr.common.params(CommonParams.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-dev.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-dev.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-dev.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductModelParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/SolrProductModelParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ParseEvent.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/event/ParseEvent.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;StandaloneSolrTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/StandaloneSolrTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrConnectionTest.java&quot; tooltip=&quot;market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/test/java/tr/gov/tubitak/mavp/indexer/SolrConnectionTest.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;category_map.txt&quot; tooltip=&quot;market-indexer/src/main/resources/category/category_map.txt&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/category/category_map.txt&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrDataModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/index/SolrDataModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ReferencePipeline.class&quot; tooltip=&quot;java.util.stream.ReferencePipeline&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Library\/Java\/JavaVirtualMachines\/jdk-21.0.6.jdk\/Contents\/Home\/lib\/jrt-fs.jar`java.base=/javadoc_location=/https:\/\/docs.oracle.com\/en\/java\/javase\/21\/docs\/api\/=/=/maven.pomderived=/true=/&amp;lt;java.util.stream(ReferencePipeline.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/carrefour/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/carrefour/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;c893381 [mavp-backend]&quot; tooltip=&quot;&amp;apos;aaaa&amp;apos; - Commit in repository mavp-backend&quot;>&#xA;&lt;persistable commit=&quot;c8933811cd8efb5e0bdd2936a24bc86de6fe5ca3&quot; path=&quot;/Users/<USER>/developer/springws/mavp-backend/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.PropertiesFileEditor&quot; name=&quot;application-prod.properties&quot; tooltip=&quot;mavp-backend/src/main/resources/application-prod.properties&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/resources/application-prod.properties&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CorsConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/CorsConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;WebMvcConfigurer.class&quot; tooltip=&quot;org.springframework.web.servlet.config.annotation.WebMvcConfigurer&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-webmvc\/6.1.15\/spring-webmvc-6.1.15.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-webmvc=/=/maven.version=/6.1.15=/=/maven.scope=/compile=/=/maven.pomderived=/true=/&amp;lt;org.springframework.web.servlet.config.annotation(WebMvcConfigurer.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.m2e.editor.MavenPomEditor&quot; name=&quot;pom.xml&quot; tooltip=&quot;mavp-backend/pom.xml&quot;>&#xA;&lt;persistable path=&quot;/mavp-backend/pom.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpStringUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpStringUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;AppInitializer.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/AppInitializer.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SftpConfig.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/SftpConfig.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MvpFileUtils.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/common/MvpFileUtils.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;depot.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/depot.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/depot.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/DepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileConfigData.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/config/FileConfigData.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;.gitignore&quot; tooltip=&quot;market-indexer/.gitignore&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/.gitignore&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;offer.json&quot; tooltip=&quot;market-indexer/sftp-depots_test/a101/offer.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/sftp-depots_test/a101/offer.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;additional-spring-configuration-metadata.json&quot; tooltip=&quot;market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/META-INF/additional-spring-configuration-metadata.json&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategryMappingFromFile.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/CategryMappingFromFile.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;docker-compose.yml&quot; tooltip=&quot;market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/compose-solr-child/docker-compose.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.genericeditor.GenericEditor&quot; name=&quot;solrconfig.xml&quot; tooltip=&quot;market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/solr-config/solr-compose-single-prod/data/conf/solrconfig.xml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;CloudHttp2SolrClient$Builder.class&quot; tooltip=&quot;org.apache.solr.client.solrj.impl.CloudHttp2SolrClient$Builder&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=mavp-backend/\/Users\/<USER>\/.m2\/repository\/org\/apache\/solr\/solr-solrj\/9.6.0\/solr-solrj-9.6.0.jar=/maven.pomderived=/true=/=/maven.groupId=/org.apache.solr=/=/maven.artifactId=/solr-solrj=/=/maven.version=/9.6.0=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.apache.solr.client.solrj.impl(CloudHttp2SolrClient$Builder.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrProductParser.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/SolrProductParser.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationEventPublisher.class&quot; tooltip=&quot;org.springframework.context.ApplicationEventPublisher&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context(ApplicationEventPublisher.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.jdt.ui.ClassFileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.ClassFileEditor&quot; name=&quot;ApplicationListenerMethodAdapter.class&quot; tooltip=&quot;org.springframework.context.event.ApplicationListenerMethodAdapter&quot;>&#xA;&lt;persistable org.eclipse.jdt.ui.ClassFileIdentifier=&quot;=market-indexer/\/Users\/<USER>\/.m2\/repository\/org\/springframework\/spring-context\/6.1.6\/spring-context-6.1.6.jar=/maven.pomderived=/true=/=/maven.groupId=/org.springframework=/=/maven.artifactId=/spring-context=/=/maven.version=/6.1.6=/=/maven.scope=/compile=/=/maven.pomderived=/true=/=/org.eclipse.jst.component.nondependency=/=/&amp;lt;org.springframework.context.event(ApplicationListenerMethodAdapter.class&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MavpFileWatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MavpFileWatcher.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;FileDownloadCompletionCallback.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/sftp/FileDownloadCompletionCallback.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;EventCompletionTracker.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/EventCompletionTracker.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;OfferDepotJsonModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/json/OfferDepotJsonModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryModel.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/model/csv/CategoryModel.java&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;SpringBootYMLPropertyEditor&quot; name=&quot;application-test.yml&quot; tooltip=&quot;market-indexer/src/main/resources/application-test.yml&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/resources/application-test.yml&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;7e52ba2 [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;7e52ba21dbe1922e0723e611616263b1b93b0d72&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.egit.ui.internal.commit.CommitEditorInputFactory&quot; id=&quot;org.eclipse.egit.ui.commitEditor&quot; name=&quot;ac6482c [market-indexer]&quot; tooltip=&quot;&amp;apos;a&amp;apos; - Commit in repository market-indexer&quot;>&#xA;&lt;persistable commit=&quot;ac6482cda4d410eedb54cce14c8bc5e9179a4a18&quot; path=&quot;/Users/<USER>/developer/springws/market-indexer/.git&quot; stash=&quot;true&quot;/>&#xA;&lt;/file>&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;CategoryMatchingService.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;>&#xA;&lt;persistable path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/CategoryMatchingService.java&quot;/>&#xA;&lt;/file>&#xA;&lt;/mruList>&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_iZAzsUIZEfC3to_rLoZHFQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_iZAzskIZEfC3to_rLoZHFQ" label="%trimmedwindow.label.eclipseSDK" x="204" y="25" width="1512" height="874">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1742906471787"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;show_in_time>&#xA;&lt;id IMemento.internal.id=&quot;org.eclipse.ui.navigator.ProjectExplorer&quot;/>&#xA;&lt;/show_in_time>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_iZAzskIZEfC3to_rLoZHFQ" selectedElement="_iZAzs0IZEfC3to_rLoZHFQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_iZAzs0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_iZAz1kIZEfC3to_rLoZHFQ">
        <children xsi:type="advanced:Perspective" xmi:id="_iZAztEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_iZAztUIZEfC3to_rLoZHFQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$&#x21e7;&#x2318;L</tags>
          <tags>persp.editorOnboardingCommand:New$$$&#x2318;N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$&#x21e7;&#x2318;T</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_iZAztUIZEfC3to_rLoZHFQ" selectedElement="_iZAzwUIZEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_iZAztkIZEfC3to_rLoZHFQ" containerData="2500" selectedElement="_iZAzt0IZEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_iZAzt0IZEfC3to_rLoZHFQ" containerData="6000" selectedElement="_iZAzuEIZEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartStack" xmi:id="_iZAzuEIZEfC3to_rLoZHFQ" elementId="left" containerData="6600" selectedElement="_iZAzuUIZEfC3to_rLoZHFQ">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAzuUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_iZBcYkIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAzukIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_iZBcZUIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAzu0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_iZBcZkIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAzvEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_iZBc40IZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_iZAzvUIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" containerData="3400" selectedElement="_iZAzvkIZEfC3to_rLoZHFQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAzvkIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" ref="_iZBc6EIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_iZAzv0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAzwEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_iZBc5kIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_iZAzwUIZEfC3to_rLoZHFQ" containerData="7500" selectedElement="_iZAzzUIZEfC3to_rLoZHFQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_iZAzwkIZEfC3to_rLoZHFQ" containerData="7500" selectedElement="_iZAzw0IZEfC3to_rLoZHFQ" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAzw0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_iZBcVUIZEfC3to_rLoZHFQ"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_iZAzxEIZEfC3to_rLoZHFQ" containerData="2500" selectedElement="_iZAzxUIZEfC3to_rLoZHFQ">
                  <children xsi:type="basic:PartStack" xmi:id="_iZAzxUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_iZAzxkIZEfC3to_rLoZHFQ">
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAzxkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_iZBc4EIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_iZAzx0IZEfC3to_rLoZHFQ" elementId="right" containerData="5000" selectedElement="_iZAzyEIZEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAzyEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_iZBc20IZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAzyUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_iZBc3kIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAzykIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_iZBc30IZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAzy0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_iZBc50IZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAzzEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_iZBc7EIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_iZAzzUIZEfC3to_rLoZHFQ" elementId="bottom" containerData="2500" selectedElement="_iZAz0kIZEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAzzkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_iZBcgEIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAzz0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="_iZBcg0IZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz0EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" ref="_iZBchEIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz0UIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_iZBchUIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz0kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_iZBcvEIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz00IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_iZBc10IZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz1EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_iZBc2EIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz1UIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_iZBc60IZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_iZAz1kIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_iZAz10IZEfC3to_rLoZHFQ" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.debug.ui.DisplayView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.junit.ResultView</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$&#x2318;3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_iZAz10IZEfC3to_rLoZHFQ" selectedElement="_iZAz2EIZEfC3to_rLoZHFQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_iZAz2EIZEfC3to_rLoZHFQ" containerData="6700" selectedElement="_iZAz4UIZEfC3to_rLoZHFQ" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_iZAz2UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="2500" selectedElement="_iZAz2kIZEfC3to_rLoZHFQ">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz2kIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" ref="_iZBc7UIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz20IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_iZBcZkIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz3EIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" ref="_iZBdRkIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Server</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz3UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" toBeRendered="false" ref="_iZBcYkIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz3kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_iZBcZUIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz30IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" ref="_iZBc40IZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_iZAz4EIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" ref="_iZBchUIZEfC3to_rLoZHFQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_iZAz4UIZEfC3to_rLoZHFQ" containerData="7500" selectedElement="_iZAz4kIZEfC3to_rLoZHFQ">
                <children xsi:type="basic:PartSashContainer" xmi:id="_iZAz4kIZEfC3to_rLoZHFQ" containerData="5884" selectedElement="_iZAz40IZEfC3to_rLoZHFQ" horizontal="true">
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz40IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" containerData="7188" ref="_iZBcVUIZEfC3to_rLoZHFQ"/>
                  <children xsi:type="basic:PartStack" xmi:id="_iZAz5EIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="2812" selectedElement="_iZAz5kIZEfC3to_rLoZHFQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz5UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" ref="_iZBdBEIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz5kIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" ref="_iZBdFUIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz50IZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" ref="_iZBdVkIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Version Control (Team)</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz6EIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" ref="_iZBdLkIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Debug</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz6UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_iZBc20IZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz6kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_iZBdREIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz60IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_iZBc30IZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz7EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_iZBc7EIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz7UIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" toBeRendered="false" ref="_iZBc6EIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Other</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_iZAz7kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" ref="_iZBdXEIZEfC3to_rLoZHFQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_iZAz70IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="4116" selectedElement="_iZAz8EIZEfC3to_rLoZHFQ">
                  <tags>Git</tags>
                  <tags>Version Control (Team)</tags>
                  <tags>JRebel</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz8EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_iZBcvEIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz8UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" ref="_iZBcgEIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz8kIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_iZBdA0IZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz80IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_iZBc10IZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz9EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" ref="_iZBc2EIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz9UIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_iZBdRUIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz9kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" ref="_iZBdSUIZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz90IZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_iZBc60IZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Terminal</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_iZAz-EIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" ref="_iZBdU0IZEfC3to_rLoZHFQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Git</tags>
                  </children>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartStack" xmi:id="_iZAz-UIZEfC3to_rLoZHFQ" elementId="PartStack@2e2662d8" toBeRendered="false" containerData="3300">
              <children xsi:type="basic:Part" xmi:id="_iZAz-kIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" closeable="true">
                <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
                <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
                <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
                <tags>View</tags>
                <tags>inject</tags>
                <tags>categoryTag:JRebel</tags>
                <tags>NoRestore</tags>
                <menus xmi:id="_iZAz-0IZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart">
                  <tags>ViewMenu</tags>
                  <tags>menuContribution:menu</tags>
                </menus>
                <toolbar xmi:id="_iZAz_EIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" visible="false"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_iZAz_UIZEfC3to_rLoZHFQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_iZAz_kIZEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_iZBcUkIZEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_iZAz_0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_iZBcU0IZEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_iZA0AEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_iZBcVEIZEfC3to_rLoZHFQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcUkIZEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcU0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcVEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_iZBcVUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss" selectedElement="_iZBcVkIZEfC3to_rLoZHFQ">
      <children xsi:type="basic:PartStack" xmi:id="_iZBcVkIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_iZBcV0IZEfC3to_rLoZHFQ">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_iZBcV0IZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductRepositorySolrJImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductRepositorySolrJImpl.java&quot; partName=&quot;ProductRepositorySolrJImpl.java&quot; title=&quot;ProductRepositorySolrJImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/solr/repository/ProductRepositorySolrJImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;10&quot; selectionOffset=&quot;8640&quot; selectionTopPixel=&quot;2908&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
          <tags>active</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_iZBcW0IZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="SolrBeanConfig.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;SolrBeanConfig.java&quot; partName=&quot;SolrBeanConfig.java&quot; title=&quot;SolrBeanConfig.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/config/SolrBeanConfig.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1098&quot; selectionTopPixel=&quot;98&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_iZBcXEIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchServiceImpl.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchServiceImpl.java&quot; partName=&quot;GeneralSearchServiceImpl.java&quot; title=&quot;GeneralSearchServiceImpl.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/service/GeneralSearchServiceImpl.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;279&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;8784&quot; selectionTopPixel=&quot;2786&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_iZBcXUIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="BaseSearchDto.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;BaseSearchDto.java&quot; partName=&quot;BaseSearchDto.java&quot; title=&quot;BaseSearchDto.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/api/BaseSearchDto.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;6&quot; selectionOffset=&quot;1232&quot; selectionTopPixel=&quot;70&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_iZBcXkIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="DepotRepository.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;DepotRepository.java&quot; partName=&quot;DepotRepository.java&quot; title=&quot;DepotRepository.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/data/depot/repository/DepotRepository.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;16&quot; selectionOffset=&quot;217&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_iZBcX0IZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="GeneralSearchControllerv2.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;GeneralSearchControllerv2.java&quot; partName=&quot;GeneralSearchControllerv2.java&quot; title=&quot;GeneralSearchControllerv2.java&quot; tooltip=&quot;mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mavp-backend/src/main/java/tr/gov/tubitak/mavp/controller/GeneralSearchControllerv2.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1597&quot; selectionTopPixel=&quot;0&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_iZBcYEIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="MatcherManager.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;MatcherManager.java&quot; partName=&quot;MatcherManager.java&quot; title=&quot;MatcherManager.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/MatcherManager.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;929&quot; selectionOffset=&quot;4708&quot; selectionTopPixel=&quot;841&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_iZBcYUIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="ProductMatcher.java" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/obj16/jcu_obj.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;editor id=&quot;org.eclipse.jdt.ui.CompilationUnitEditor&quot; name=&quot;ProductMatcher.java&quot; partName=&quot;ProductMatcher.java&quot; title=&quot;ProductMatcher.java&quot; tooltip=&quot;market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;>&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/market-indexer/src/main/java/tr/gov/tubitak/mavp/indexer/services/file/helper/ProductMatcher.java&quot;/>&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;14&quot; selectionOffset=&quot;5296&quot; selectionTopPixel=&quot;1358&quot;/>&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.jdt.ui.CompilationUnitEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcYkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xA;&lt;xmlDefinedFilters>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xA;&lt;/xmlDefinedFilters>&#xA;&lt;/customFilters>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_iZBcY0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBcZEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcZUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcZkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;1&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_iZBcZ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBce0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcgEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xA;&lt;expanded>&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xA;&lt;/expanded>&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;251&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;702&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_iZBcgUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBcgkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcg0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBchEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBchUIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view isPinned=&quot;false&quot;>&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_iZBchkIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBcl0IZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBcvEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_iZBcvUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBcwEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc10IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc2EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_iZBc2UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBc2kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc20IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_iZBc3EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBc3UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc3kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc30IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc4EIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xA;&lt;sorter>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xA;&lt;/sorter>&#xA;&lt;/sorter>&#xA;&lt;filteredTreeFindHistory/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_iZBc4UIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBc4kIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc40IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view failuresOnly=&quot;false&quot; ignoredOnly=&quot;false&quot; layout=&quot;1&quot; orientation=&quot;2&quot; ratio=&quot;500&quot; scroll=&quot;false&quot; sortingCriterion=&quot;1&quot; time=&quot;true&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>highlighted</tags>
      <menus xmi:id="_iZBc5EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBc5UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc5kIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc50IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc6EIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_iZBc6UIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBc6kIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc60IZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc7EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBc7UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_iZBc7kIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBc-UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdA0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdBEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_iZBdBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdD0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdFUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_iZBdFkIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdIEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdLkIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_iZBdL0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdOUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdREIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdRUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdRkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot;>&#xA;&lt;lastRecentlyUsedFilters/>&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
      <menus xmi:id="_iZBdR0IZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdSEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdSUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_iZBdSkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdTEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdU0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
      <menus xmi:id="_iZBdVEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdVUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdVkIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Version Control (Team)</tags>
      <menus xmi:id="_iZBdV0IZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdWEIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdWUIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
      <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_iZBdWkIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdW0IZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_iZBdXEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xA;&lt;view search_scope_type=&quot;1&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <menus xmi:id="_iZBdXUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_iZBdXkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_iZBdX0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_iZBdYEIZEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_iZBdYUIZEfC3to_rLoZHFQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZBdYkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_iZBdakIZEfC3to_rLoZHFQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_iZDSqUIZEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZBdb0IZEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_iZBdcEIZEfC3to_rLoZHFQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZBdcUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_iZBdc0IZEfC3to_rLoZHFQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" command="_iZDQ20IZEfC3to_rLoZHFQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_iZBddEIZEfC3to_rLoZHFQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_iZDRXEIZEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZBddUIZEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_iZBddkIZEfC3to_rLoZHFQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCB3EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCB5kIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCB7UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCB7kIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCB80IZEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_iZCB9EIZEfC3to_rLoZHFQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCB9UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_iZCB-0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_iZDSYkIZEfC3to_rLoZHFQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCCAEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CompilationUnitEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCCAUIZEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_iZCCAkIZEfC3to_rLoZHFQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCCA0IZEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_iZCCBEIZEfC3to_rLoZHFQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_iZCCBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCCEIZEfC3to_rLoZHFQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCDEIZEfC3to_rLoZHFQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_iZCCE0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCFEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCFUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCFkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_iZCCGkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCG0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_iZCCHEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCHUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCHkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_iZCCH0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <handlers xmi:id="_iZCCIEIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ArrangeWindowHandler" command="_iZDTXkIZEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_iZCCIUIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.MinimizeWindowHandler" command="_iZDTX0IZEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_iZCCIkIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.ZoomWindowHandler" command="_iZDTYEIZEfC3to_rLoZHFQ"/>
  <handlers xmi:id="_iZCCI0IZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CloseDialogHandler" command="_iZDTYUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCCJEIZEfC3to_rLoZHFQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_iZCD6UIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCCJUIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+SPACE" command="_iZDQuUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCJkIZEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+F" command="_iZDQQEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCJ0IZEfC3to_rLoZHFQ" keySequence="SHIFT+F10" command="_iZDQl0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCKEIZEfC3to_rLoZHFQ" keySequence="ALT+PAGE_UP" command="_iZDRa0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCKUIZEfC3to_rLoZHFQ" keySequence="ALT+PAGE_DOWN" command="_iZDSK0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCKkIZEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_iZDQHkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCK0IZEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_UP" command="_iZDSwkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCLEIZEfC3to_rLoZHFQ" keySequence="CTRL+PAGE_DOWN" command="_iZDQpUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCLUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F1" command="_iZDQV0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCLkIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F2" command="_iZDSE0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCL0IZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F3" command="_iZDStkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCMEIZEfC3to_rLoZHFQ" keySequence="COMMAND+X" command="_iZDQ4UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCMUIZEfC3to_rLoZHFQ" keySequence="COMMAND+Z" command="_iZDQ20IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCMkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Z" command="_iZDRXEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCM0IZEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_iZDQnEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCNEIZEfC3to_rLoZHFQ" keySequence="COMMAND+6" command="_iZDQxEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCNUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+I" command="_iZDQe0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCNkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+L" command="_iZDS4UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCN0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+D" command="_iZDTC0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCOEIZEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_iZDP_kIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCOUIZEfC3to_rLoZHFQ" keySequence="COMMAND+A" command="_iZDRMUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCOkIZEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_iZDRkUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCO0IZEfC3to_rLoZHFQ" keySequence="ALT+SPACE" command="_iZDSgUIZEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_iZCCPEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_iZCD6kIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCCPUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q B" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCPkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_iZCCP0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q C" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCQEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_iZCCQUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q D" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCQkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_iZCCQ0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q O" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCREIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_iZCCRUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q P" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCRkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_iZCCR0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Q" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCSEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q S" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCSUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_iZCCSkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q T" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCS0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_iZCCTEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q V" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCTUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_iZCCTkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q H" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCT0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_iZCCUEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q J" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCUUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_iZCCUkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q K" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCU0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_iZCCVEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q L" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCVUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_iZCCVkIZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+SHIFT+T" command="_iZDQHUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCV0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q X" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCWEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_iZCCWUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Y" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCWkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_iZCCW0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Q Z" command="_iZDSHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCXEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_iZCCXUIZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+B" command="_iZDSJEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCXkIZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+P" command="_iZDQREIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCX0IZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+T" command="_iZDRjEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCYEIZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_iZDQFkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCYUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+A" command="_iZDSOUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCYkIZEfC3to_rLoZHFQ" keySequence="CTRL+Q" command="_iZDSvEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCY0IZEfC3to_rLoZHFQ" keySequence="CTRL+H" command="_iZDSgEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCZEIZEfC3to_rLoZHFQ" keySequence="CTRL+M" command="_iZDSfEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCZUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+P" command="_iZDSFUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCZkIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+H" command="_iZDQykIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCZ0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+L" command="_iZDRyUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCaEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+M" command="_iZDTAUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCaUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_iZDR-0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCakIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_iZDQvkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCa0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F7" command="_iZDTAkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCbEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F8" command="_iZDQu0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCbUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F9" command="_iZDRJ0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCbkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F10" command="_iZDRo0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCb0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_LEFT" command="_iZDQIkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCcEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_RIGHT" command="_iZDQ80IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCcUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F11" command="_iZDS0EIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCckIZEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_iZDR7EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCc0IZEfC3to_rLoZHFQ" keySequence="SHIFT+F5" command="_iZDRQ0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCdEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F6" command="_iZDSBUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCdUIZEfC3to_rLoZHFQ" keySequence="ALT+F7" command="_iZDRvEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCdkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F12" command="_iZDQVEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCd0IZEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_iZDRKkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCeEIZEfC3to_rLoZHFQ" keySequence="COMMAND+F7" command="_iZDRkkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCeUIZEfC3to_rLoZHFQ" keySequence="COMMAND+F8" command="_iZDQn0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCekIZEfC3to_rLoZHFQ" keySequence="COMMAND+F9" command="_iZDQZUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCe0IZEfC3to_rLoZHFQ" keySequence="COMMAND+F11" command="_iZDS_UIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCfEIZEfC3to_rLoZHFQ" keySequence="COMMAND+F12" command="_iZDSg0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCfUIZEfC3to_rLoZHFQ" keySequence="F2" command="_iZDQA0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCfkIZEfC3to_rLoZHFQ" keySequence="F3" command="_iZDQlUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCf0IZEfC3to_rLoZHFQ" keySequence="F4" command="_iZDQDEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCgEIZEfC3to_rLoZHFQ" keySequence="F5" command="_iZDQ_EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCgUIZEfC3to_rLoZHFQ" keySequence="COMMAND+F6" command="_iZDQRUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCgkIZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_iZDSvEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCg0IZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_iZDQW0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCChEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X O" command="_iZDR7UIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCChUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X P" command="_iZDTGEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCChkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X Q" command="_iZDQeUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCh0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X T" command="_iZDRL0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCiEIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X M" command="_iZDROEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCiUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X B" command="_iZDTVEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCikIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_UP" command="_iZDR50IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCi0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_DOWN" command="_iZDTQkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCjEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+ARROW_RIGHT" command="_iZDR0UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCjUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+F7" command="_iZDSPUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCjkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+F12" command="_iZDTE0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCj0IZEfC3to_rLoZHFQ" keySequence="COMMAND+[" command="_iZDQIkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCkEIZEfC3to_rLoZHFQ" keySequence="COMMAND+]" command="_iZDQ80IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCkUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Z" command="_iZDRf0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCkkIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+X R" command="_iZDRW0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCk0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X G" command="_iZDS-UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCClEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X J" command="_iZDSL0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCClUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+[" command="_iZDQvEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCClkIZEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_iZCCl0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X A" command="_iZDQAEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCmEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+X E" command="_iZDSEEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCmUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+R" command="_iZDTWUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCmkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+S" command="_iZDR_EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCm0IZEfC3to_rLoZHFQ" keySequence="COMMAND+3" command="_iZDQpkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCnEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+C" command="_iZDShUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCnUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_iZDQ4EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCnkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_iZDQ6UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCn0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+U" command="_iZDQZkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCoEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_iZDS0kIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCoUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+F" command="_iZDSwEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCokIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+G" command="_iZDSEUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCo0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+W" command="_iZDQ30IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCpEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+H" command="_iZDRhUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCpUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+K" command="_iZDQUkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCpkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+N" command="_iZDRpUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCp0IZEfC3to_rLoZHFQ" keySequence="COMMAND+." command="_iZDTJ0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCqEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_iZDTBUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCqUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_iZDSKUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCqkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+B" command="_iZDQU0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCq0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_iZDRYUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCrEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_iZDRekIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCrUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+T" command="_iZDRtUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCrkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+E" command="_iZDQYUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCr0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+V" command="_iZDRHkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCsEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_iZDTNEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCsUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+W" command="_iZDTIkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCskIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+I" command="_iZDQIEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCs0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+J" command="_iZDQzkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCtEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+K" command="_iZDRGEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCtUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+L" command="_iZDQmEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCtkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_iZDTEkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCt0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_iZDQ3EIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCuEIZEfC3to_rLoZHFQ" keySequence="COMMAND+P" command="_iZDSqUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCuUIZEfC3to_rLoZHFQ" keySequence="COMMAND+S" command="_iZDRFUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCukIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B D" command="_iZDTDkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCu0IZEfC3to_rLoZHFQ" keySequence="COMMAND+U" command="_iZDRV0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCvEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B F" command="_iZDQf0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCvUIZEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_iZDRZUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCvkIZEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_iZDSb0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCv0IZEfC3to_rLoZHFQ" keySequence="COMMAND+K" command="_iZDSJkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCwEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+-" command="_iZDQvEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
      <parameters xmi:id="_iZCCwUIZEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_iZCCwkIZEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_iZDTOUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCw0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_iZDQAUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCxEIZEfC3to_rLoZHFQ" keySequence="COMMAND+B" command="_iZDQBEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCxUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B R" command="_iZDR1kIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCxkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+SHIFT+B S" command="_iZDSbEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCx0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+3" command="_iZDQH0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCyEIZEfC3to_rLoZHFQ" keySequence="COMMAND+E" command="_iZDQ0EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCyUIZEfC3to_rLoZHFQ" keySequence="COMMAND+F" command="_iZDQOUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCykIZEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_iZCtDkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCCy0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D E" command="_iZDTS0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCzEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D A" command="_iZDSl0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCzUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D T" command="_iZCtGkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCzkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D J" command="_iZDSWkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCCz0IZEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_iZDSb0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC0EIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D O" command="_iZDRlkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC0UIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D P" command="_iZDSpUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC0kIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+CTRL+D Q" command="_iZDRSkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC00IZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D B" command="_iZDTCEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC1EIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+D R" command="_iZDRXUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC1UIZEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_iZDT00IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC1kIZEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_iZDTzkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC10IZEfC3to_rLoZHFQ" keySequence="COMMAND+BS" command="_iZDQSkIZEfC3to_rLoZHFQ">
      <tags>type:user</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_iZCC2EIZEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_iZCEEEIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCC2UIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+P" command="_iZDShEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC2kIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+D" command="_iZDRVkIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCC20IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_iZCD7kIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCC3EIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+Q" command="_iZDQgUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC3UIZEfC3to_rLoZHFQ" keySequence="CTRL+." command="_iZDS8EIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC3kIZEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_iZDQzUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC30IZEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_MULTIPLY" command="_iZDST0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC4EIZEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_ADD" command="_iZDTEUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC4UIZEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_SUBTRACT" command="_iZDSs0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC4kIZEfC3to_rLoZHFQ" keySequence="COMMAND+NUMPAD_DIVIDE" command="_iZDQWUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC40IZEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_iZDRykIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC5EIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_MULTIPLY" command="_iZDSVUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC5UIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+NUMPAD_DIVIDE" command="_iZDR20IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC5kIZEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_iZDTRkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC50IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_UP" command="_iZDTI0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC6EIZEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_iZDSN0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC6UIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+ARROW_DOWN" command="_iZDRPUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC6kIZEfC3to_rLoZHFQ" keySequence="ALT+ARROW_LEFT" command="_iZDRi0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC60IZEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_iZDQgEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC7EIZEfC3to_rLoZHFQ" keySequence="SHIFT+END" command="_iZDRiUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC7UIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+INSERT" command="_iZDQZ0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC7kIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_LEFT" command="_iZDRA0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC70IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+ARROW_RIGHT" command="_iZDRHEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC8EIZEfC3to_rLoZHFQ" keySequence="SHIFT+HOME" command="_iZDSdUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC8UIZEfC3to_rLoZHFQ" keySequence="COMMAND+F10" command="_iZDSr0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC8kIZEfC3to_rLoZHFQ" keySequence="COMMAND+END" command="_iZDSO0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC80IZEfC3to_rLoZHFQ" keySequence="END" command="_iZDSO0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC9EIZEfC3to_rLoZHFQ" keySequence="INSERT" command="_iZDR1UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC9UIZEfC3to_rLoZHFQ" keySequence="F2" command="_iZDQp0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC9kIZEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_iZDS7UIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC90IZEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_RIGHT" command="_iZDSz0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC-EIZEfC3to_rLoZHFQ" keySequence="COMMAND+HOME" command="_iZDP_UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC-UIZEfC3to_rLoZHFQ" keySequence="HOME" command="_iZDP_UIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC-kIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_iZDRH0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC-0IZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_iZDQbkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC_EIZEfC3to_rLoZHFQ" keySequence="ALT+DEL" command="_iZDQ00IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC_UIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+DEL" command="_iZDShkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCC_kIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+Y" command="_iZCtBEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCC_0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+X" command="_iZDRmkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDAEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+Y" command="_iZDREEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDAUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_iZDRykIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDAkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+A" command="_iZDRskIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDA0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+J" command="_iZDQdkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDBEIZEfC3to_rLoZHFQ" keySequence="COMMAND++" command="_iZDSCEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDBUIZEfC3to_rLoZHFQ" keySequence="COMMAND+-" command="_iZDRE0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDBkIZEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_iZCtGEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDB0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_iZCtH0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDCEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_iZCtGEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDCUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_iZDQw0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDCkIZEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_iZDTIUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDC0IZEfC3to_rLoZHFQ" keySequence="COMMAND+J" command="_iZDQJUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDDEIZEfC3to_rLoZHFQ" keySequence="COMMAND+L" command="_iZDSlUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDDUIZEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_iZDS_0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDDkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_iZDQzUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDD0IZEfC3to_rLoZHFQ" keySequence="COMMAND+D" command="_iZDQMUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDEEIZEfC3to_rLoZHFQ" keySequence="COMMAND+=" command="_iZDSCEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDEUIZEfC3to_rLoZHFQ" keySequence="SHIFT+CR" command="_iZDS7EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDEkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+CR" command="_iZDStUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDE0IZEfC3to_rLoZHFQ" keySequence="ALT+BS" command="_iZCtDUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_iZCDFEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_iZCD80IZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDFUIZEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+/" command="_iZDRWEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDFkIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_iZDRYUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDF0IZEfC3to_rLoZHFQ" keySequence="COMMAND+CTRL+\" command="_iZDQMkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDGEIZEfC3to_rLoZHFQ" keySequence="COMMAND+F3" command="_iZDTJUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDGUIZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_UP" command="_iZDRaEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDGkIZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_iZDRNkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDG0IZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+END" command="_iZDQLUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDHEIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_UP" command="_iZDRSUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDHUIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+PAGE_DOWN" command="_iZDQVkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDHkIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+HOME" command="_iZDQ-kIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDH0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_iZDQMkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDIEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_iZDSPEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDIUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_iZDTSkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDIkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+T" command="_iZDQ4EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDI0IZEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_iZDRqEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDJEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_iZDQqEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDJUIZEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_iZDRqEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDJkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_iZDRqEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDJ0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+U" command="_iZDSYUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDKEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_iZDS60IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDKUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_iZDQ1kIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDKkIZEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_iZDRsUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDK0IZEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_iZDQ8UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDLEIZEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_iZDRMkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDLUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+'" command="_iZDRw0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDLkIZEfC3to_rLoZHFQ" keySequence="COMMAND+2 F" command="_iZDTEEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDL0IZEfC3to_rLoZHFQ" keySequence="COMMAND+2 R" command="_iZDScUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDMEIZEfC3to_rLoZHFQ" keySequence="COMMAND+2 T" command="_iZDRp0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDMUIZEfC3to_rLoZHFQ" keySequence="COMMAND+2 L" command="_iZDQJkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDMkIZEfC3to_rLoZHFQ" keySequence="COMMAND+2 M" command="_iZDQ_0IZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDM0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_iZCD8UIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDNEIZEfC3to_rLoZHFQ" keySequence="ALT+CTRL+H" command="_iZDSjEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDNUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_iZDQA0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDNkIZEfC3to_rLoZHFQ" keySequence="F3" command="_iZDSvUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDN0IZEfC3to_rLoZHFQ" keySequence="F4" command="_iZCtCkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDOEIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_iZDRtkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDOUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_iZDRukIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDOkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_iZDRCkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDO0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+G" command="_iZDSzkIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDPEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_iZCECEIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDPUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_iZDReEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDPkIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_iZDRHUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDP0IZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_iZDSRkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDQEIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_iZDRFEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDQUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+N" command="_iZDQR0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDQkIZEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_iZDSGUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDQ0IZEfC3to_rLoZHFQ" keySequence="ALT+ARROW_DOWN" command="_iZDQzEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDREIZEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_iZDQR0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDRUIZEfC3to_rLoZHFQ" keySequence="INSERT" command="_iZDRDUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDRkIZEfC3to_rLoZHFQ" keySequence="F4" command="_iZDQG0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDR0IZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_iZDSc0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDSEIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_iZDRGUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDSUIZEfC3to_rLoZHFQ" keySequence="ALT+N" command="_iZDRDUIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDSkIZEfC3to_rLoZHFQ" keySequence="COMMAND+CR" command="_iZDQyEIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDS0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_iZCD9EIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDTEIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+C" command="_iZDReEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDTUIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+R" command="_iZDRHUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDTkIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+S" command="_iZDQ5kIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDT0IZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+U" command="_iZDSRkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDUEIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+I" command="_iZDRFEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDUUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+M" command="_iZCtKUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDUkIZEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_iZDTG0IZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDU0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" bindingContext="_iZCD_UIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDVEIZEfC3to_rLoZHFQ" keySequence="CTRL+D" command="_iZDTHkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDVUIZEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_iZDRvkIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDVkIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_iZCD7EIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDV0IZEfC3to_rLoZHFQ" keySequence="ALT+ARROW_UP" command="_iZCtDEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDWEIZEfC3to_rLoZHFQ" keySequence="ALT+ARROW_RIGHT" command="_iZDS_EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDWUIZEfC3to_rLoZHFQ" keySequence="SHIFT+INSERT" command="_iZDRCEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDWkIZEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_iZDSZkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDW0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+V" command="_iZDRCEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDXEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_iZDSZkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDXUIZEfC3to_rLoZHFQ" keySequence="COMMAND+V" command="_iZDRCEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDXkIZEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_iZDSZkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_iZCDX0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_iZCD-0IZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDYEIZEfC3to_rLoZHFQ" keySequence="SHIFT+F2" command="_iZDRxUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDYUIZEfC3to_rLoZHFQ" keySequence="F3" command="_iZCtHUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDYkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_iZDQC0IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDY0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_iZDS60IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDZEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+O" command="_iZCtFkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_iZCDZUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_iZCEA0IZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDZkIZEfC3to_rLoZHFQ" keySequence="ALT+F5" command="_iZDS-EIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDZ0IZEfC3to_rLoZHFQ" keySequence="F7" command="_iZDTK0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDaEIZEfC3to_rLoZHFQ" keySequence="F8" command="_iZDRz0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDaUIZEfC3to_rLoZHFQ" keySequence="COMMAND+F2" command="_iZDSiUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDakIZEfC3to_rLoZHFQ" keySequence="F5" command="_iZDQEUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDa0IZEfC3to_rLoZHFQ" keySequence="F6" command="_iZDRIEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDbEIZEfC3to_rLoZHFQ" keySequence="COMMAND+R" command="_iZDRlUIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDbUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_iZCEAEIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDbkIZEfC3to_rLoZHFQ" keySequence="COMMAND+INSERT" command="_iZDR9kIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDb0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_iZCD9UIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDcEIZEfC3to_rLoZHFQ" keySequence="F1" command="_iZCtG0IZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDcUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_iZCEC0IZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDckIZEfC3to_rLoZHFQ" keySequence="F2" command="_iZDQTUIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDc0IZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_iZCD-EIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDdEIZEfC3to_rLoZHFQ" keySequence="F3" command="_iZDRsEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDdUIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_iZDQFEIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDdkIZEfC3to_rLoZHFQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_iZDR70IZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDd0IZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_UP" command="_iZDSMkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDeEIZEfC3to_rLoZHFQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_iZDSSEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDeUIZEfC3to_rLoZHFQ" keySequence="COMMAND+\" command="_iZDSEkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDekIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+\" command="_iZDSEkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDe0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+P" command="_iZDRs0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDfEIZEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_iZDRokIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
    <bindings xmi:id="_iZCDfUIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+A" command="_iZDTVkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDfkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_iZDS6UIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDf0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_iZDTNUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDgEIZEfC3to_rLoZHFQ" keySequence="COMMAND+I" command="_iZDSrkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDgUIZEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_iZDRwkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDgkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+/" command="_iZDRokIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDg0IZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_iZCECUIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDhEIZEfC3to_rLoZHFQ" keySequence="F5" command="_iZDS6EIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDhUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_iZCECkIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDhkIZEfC3to_rLoZHFQ" keySequence="COMMAND+ARROW_LEFT" command="_iZDQLkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDh0IZEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_iZDQsEIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDiEIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_iZCEB0IZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDiUIZEfC3to_rLoZHFQ" keySequence="ALT+Y" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDikIZEfC3to_rLoZHFQ" keySequence="ALT+A" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDi0IZEfC3to_rLoZHFQ" keySequence="ALT+B" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDjEIZEfC3to_rLoZHFQ" keySequence="ALT+C" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDjUIZEfC3to_rLoZHFQ" keySequence="ALT+D" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDjkIZEfC3to_rLoZHFQ" keySequence="ALT+E" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDj0IZEfC3to_rLoZHFQ" keySequence="ALT+F" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDkEIZEfC3to_rLoZHFQ" keySequence="ALT+G" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDkUIZEfC3to_rLoZHFQ" keySequence="ALT+P" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDkkIZEfC3to_rLoZHFQ" keySequence="ALT+R" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDk0IZEfC3to_rLoZHFQ" keySequence="ALT+S" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDlEIZEfC3to_rLoZHFQ" keySequence="ALT+T" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDlUIZEfC3to_rLoZHFQ" keySequence="ALT+V" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDlkIZEfC3to_rLoZHFQ" keySequence="ALT+W" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDl0IZEfC3to_rLoZHFQ" keySequence="ALT+H" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDmEIZEfC3to_rLoZHFQ" keySequence="ALT+L" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDmUIZEfC3to_rLoZHFQ" keySequence="ALT+N" command="_iZDRoEIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDmkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_iZCD8kIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDm0IZEfC3to_rLoZHFQ" keySequence="COMMAND+1" command="_iZDTMUIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDnEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_iZCED0IZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDnUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+B" command="_iZDTSkIZEfC3to_rLoZHFQ">
      <tags>platform:cocoa</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_iZCDnkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="_iZCEAkIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDn0IZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+D" command="_iZDSKkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDoEIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+P" command="_iZDSQkIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDoUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+R" command="_iZDTKEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDokIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+S" command="_iZDQjUIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDo0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_iZCD_EIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDpEIZEfC3to_rLoZHFQ" keySequence="COMMAND+7" command="_iZDRqEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDpUIZEfC3to_rLoZHFQ" keySequence="COMMAND+/" command="_iZDRqEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDpkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+C" command="_iZDRqEIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDp0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_iZCD90IZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDqEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+O" command="_iZDP-EIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDqUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_iZCD8EIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDqkIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+F" command="_iZDQK0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDq0IZEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_iZDQP0IZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDrEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_iZCEAUIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDrUIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+M" command="_iZDRLEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDrkIZEfC3to_rLoZHFQ" keySequence="ALT+COMMAND+N" command="_iZDTFEIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDr0IZEfC3to_rLoZHFQ" keySequence="COMMAND+T" command="_iZDQq0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDsEIZEfC3to_rLoZHFQ" keySequence="COMMAND+W" command="_iZDR4EIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDsUIZEfC3to_rLoZHFQ" keySequence="COMMAND+N" command="_iZDSC0IZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDskIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_iZCEBEIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDs0IZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+," command="_iZDSv0IZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDtEIZEfC3to_rLoZHFQ" keySequence="COMMAND+SHIFT+." command="_iZDSeUIZEfC3to_rLoZHFQ"/>
    <bindings xmi:id="_iZCDtUIZEfC3to_rLoZHFQ" keySequence="COMMAND+G" command="_iZDSekIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDtkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_iZCD70IZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDt0IZEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_iZDRSEIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDuEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_iZCD9kIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDuUIZEfC3to_rLoZHFQ" keySequence="COMMAND+O" command="_iZDP-EIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDukIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_iZCEBkIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDu0IZEfC3to_rLoZHFQ" keySequence="COMMAND+C" command="_iZDQRkIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDvEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_iZCD7UIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDvUIZEfC3to_rLoZHFQ" keySequence="ALT+CR" command="_iZDRhEIZEfC3to_rLoZHFQ"/>
  </bindingTables>
  <bindingTables xmi:id="_iZCDvkIZEfC3to_rLoZHFQ" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" bindingContext="_iZCEDEIZEfC3to_rLoZHFQ">
    <bindings xmi:id="_iZCDv0IZEfC3to_rLoZHFQ" keySequence="M1+W" command="_iZDTYUIZEfC3to_rLoZHFQ">
      <tags>deleted</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_iZCDwEIZEfC3to_rLoZHFQ" bindingContext="_iZCEEUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDwUIZEfC3to_rLoZHFQ" bindingContext="_iZCEEkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDwkIZEfC3to_rLoZHFQ" bindingContext="_iZCEE0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDw0IZEfC3to_rLoZHFQ" bindingContext="_iZCEFEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDxEIZEfC3to_rLoZHFQ" bindingContext="_iZCEFUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDxUIZEfC3to_rLoZHFQ" bindingContext="_iZCEFkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDxkIZEfC3to_rLoZHFQ" bindingContext="_iZCEF0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDx0IZEfC3to_rLoZHFQ" bindingContext="_iZCEGEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDyEIZEfC3to_rLoZHFQ" bindingContext="_iZCEGUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDyUIZEfC3to_rLoZHFQ" bindingContext="_iZCEGkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDykIZEfC3to_rLoZHFQ" bindingContext="_iZCEG0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDy0IZEfC3to_rLoZHFQ" bindingContext="_iZCEHEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDzEIZEfC3to_rLoZHFQ" bindingContext="_iZCEHUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDzUIZEfC3to_rLoZHFQ" bindingContext="_iZCEHkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDzkIZEfC3to_rLoZHFQ" bindingContext="_iZCEH0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCDz0IZEfC3to_rLoZHFQ" bindingContext="_iZCEIEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD0EIZEfC3to_rLoZHFQ" bindingContext="_iZCEIUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD0UIZEfC3to_rLoZHFQ" bindingContext="_iZCEIkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD0kIZEfC3to_rLoZHFQ" bindingContext="_iZCEI0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD00IZEfC3to_rLoZHFQ" bindingContext="_iZCEJEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD1EIZEfC3to_rLoZHFQ" bindingContext="_iZCEJUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD1UIZEfC3to_rLoZHFQ" bindingContext="_iZCEJkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD1kIZEfC3to_rLoZHFQ" bindingContext="_iZCEJ0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD10IZEfC3to_rLoZHFQ" bindingContext="_iZCEKEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD2EIZEfC3to_rLoZHFQ" bindingContext="_iZCEKUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD2UIZEfC3to_rLoZHFQ" bindingContext="_iZCEKkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD2kIZEfC3to_rLoZHFQ" bindingContext="_iZCEK0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD20IZEfC3to_rLoZHFQ" bindingContext="_iZCELEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD3EIZEfC3to_rLoZHFQ" bindingContext="_iZCELUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD3UIZEfC3to_rLoZHFQ" bindingContext="_iZCELkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD3kIZEfC3to_rLoZHFQ" bindingContext="_iZCEL0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD30IZEfC3to_rLoZHFQ" bindingContext="_iZCEMEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD4EIZEfC3to_rLoZHFQ" bindingContext="_iZCEMUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD4UIZEfC3to_rLoZHFQ" bindingContext="_iZCEMkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD4kIZEfC3to_rLoZHFQ" bindingContext="_iZCEM0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD40IZEfC3to_rLoZHFQ" bindingContext="_iZCENEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD5EIZEfC3to_rLoZHFQ" bindingContext="_iZCENUIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD5UIZEfC3to_rLoZHFQ" bindingContext="_iZCENkIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD5kIZEfC3to_rLoZHFQ" bindingContext="_iZCEN0IZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD50IZEfC3to_rLoZHFQ" bindingContext="_iZCEOEIZEfC3to_rLoZHFQ"/>
  <bindingTables xmi:id="_iZCD6EIZEfC3to_rLoZHFQ" bindingContext="_iZCEOUIZEfC3to_rLoZHFQ"/>
  <rootContext xmi:id="_iZCD6UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_iZCD6kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_iZCD60IZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_iZCD7EIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_iZCD7UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_iZCD7kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_iZCD70IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_iZCD8EIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_iZCD8UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_iZCD8kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_iZCD80IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_iZCD9EIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_iZCD9UIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_iZCD9kIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_iZCD90IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_iZCD-EIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_iZCD-UIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_iZCD-kIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="_iZCD-0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_iZCD_EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_iZCD_UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_iZCD_kIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_iZCD_0IZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_iZCEAEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_iZCEAUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_iZCEAkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_iZCEA0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_iZCEBEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_iZCEBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_iZCEBkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_iZCEB0IZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_iZCECEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_iZCECUIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_iZCECkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_iZCEC0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_iZCEDEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_iZCEDUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_iZCEDkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_iZCED0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_iZCEEEIZEfC3to_rLoZHFQ" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_iZCEEUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_iZCEEkIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_iZCEE0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_iZCEFEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_iZCEFUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_iZCEFkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_iZCEF0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_iZCEGEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_iZCEGUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_iZCEGkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_iZCEG0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_iZCEHEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_iZCEHUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_iZCEHkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_iZCEH0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_iZCEIEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_iZCEIUIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_iZCEIkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_iZCEI0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_iZCEJEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_iZCEJUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_iZCEJkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_iZCEJ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_iZCEKEIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_iZCEKUIZEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_iZCEKkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_iZCEK0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_iZCELEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_iZCELUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_iZCELkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_iZCEL0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_iZCEMEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_iZCEMUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_iZCEMkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_iZCEM0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_iZCENEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_iZCENUIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_iZCENkIZEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <rootContext xmi:id="_iZCEN0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.launching.localJavaApplication.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_iZCEOEIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.springframework.ide.eclipse.boot.launch.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <rootContext xmi:id="_iZCEOUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.eclipse.jdt.junit.launchconfig.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_iZCEOkIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEO0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEPEIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEPUIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEPkIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEP0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEQEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEQUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEQkIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEQ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEREIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCERUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCERkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_iZCER0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_iZCESEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_iZCESUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_iZCESkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_iZCES0IZEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_iZCETEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_iZCETUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_iZCETkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCET0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEUEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEUUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEUkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEU0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEVEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEVUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEVkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEV0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEWEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEWUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEWkIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEW0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEXEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEXUIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEXkIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEX0IZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEYEIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEYUIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEYkIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEY0IZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEZEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEZUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEZkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEZ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEaEIZEfC3to_rLoZHFQ" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEaUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEakIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEa0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEbEIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEbUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEbkIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEb0IZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEcEIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEcUIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEckIZEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEc0IZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEdEIZEfC3to_rLoZHFQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEdUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEdkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEd0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEeEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEeUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEekIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEe0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEfEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEfUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEfkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEf0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEgEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEgUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEgkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEg0IZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEhEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEhUIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEhkIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEh0IZEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEiEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEiUIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.config.ui.JRebelConfigView" label="JRebel Configuration" iconURI="platform:/plugin/org.zeroturnaround.eclipse/icons/jrebel_16x16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.config.ui.JRebelConfigView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_iZCEikIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart" label="JRebel Setup Guide" iconURI="platform:/plugin/org.zeroturnaround.eclipse.setup-guide/icons/jrebel_16x16.png" tooltip="" category="JRebel" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.zeroturnaround.eclipse.feature.guide.SetupGuideViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.zeroturnaround.eclipse.setup-guide"/>
    <tags>View</tags>
    <tags>inject</tags>
    <tags>categoryTag:JRebel</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <trimContributions xmi:id="_iZCs9EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_iZCs9UIZEfC3to_rLoZHFQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_iZCs9kIZEfC3to_rLoZHFQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_iZCs90IZEfC3to_rLoZHFQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_iZCtAkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtA0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtBEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZCtBkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_iZCtB0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtCEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtCUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtCkIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtC0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtDEIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_iZDT_0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtDUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtDkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtD0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtEEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtEUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtEkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtE0IZEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZCtFEIZEfC3to_rLoZHFQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_iZCtFUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtFkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtF0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtGEIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="_iZDT-0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtGUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtGkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtG0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtHEIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_iZDUBEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtHUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtHkIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtH0IZEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_iZDT5UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtIEIZEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtIUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtIkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtI0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtJEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtJUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtJkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtJ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtKEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZCtKUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_iZDT6UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP8EIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP8UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP8kIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP80IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_iZDT70IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDP9EIZEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_iZDP9UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP9kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP90IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP-EIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP-UIZEfC3to_rLoZHFQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_iZDT8EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP-kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP-0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP_EIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP_UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP_kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDP_0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQAEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQAUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQAkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQA0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQBEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQBkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQB0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQCEIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQCUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQCkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQC0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQDEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQDUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQDkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_iZDUBUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQD0IZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQEEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQEUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQEkIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQE0IZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQFEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQFUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQFkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQF0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQGEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQGUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQGkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_iZDQG0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQHEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQHUIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_iZDT9UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQHkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQH0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQIEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQIUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQIkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQI0IZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQJEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQJUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQJkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQJ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQKEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQKUIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQKkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQK0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_iZDUCUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQLEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQLUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQLkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQL0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQMEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQMUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQMkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQM0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQNEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQNUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQNkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_iZDT70IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQN0IZEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_iZDQOEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQOUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQOkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQO0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQPEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQPUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_iZDQPkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQP0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQQEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQQUIZEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQQkIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_iZDT4kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQQ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQREIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQRUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQRkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQR0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQSEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQSUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQSkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQS0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQTEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQTUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQTkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQT0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQUEIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQUUIZEfC3to_rLoZHFQ" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_iZDQUkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQU0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQVEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQVUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQVkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQV0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_iZDUCEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQWEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQWUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQWkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQW0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQXEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQXUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQXkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_iZDT9EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQX0IZEfC3to_rLoZHFQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_iZDQYEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQYUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQYkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQY0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQZEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQZUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQZkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQZ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQaEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQaUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQakIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQa0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQbEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQbUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQbkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQb0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_iZDUBEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQcEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQcUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQckIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQc0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQdEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQdUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQdkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQd0IZEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQeEIZEfC3to_rLoZHFQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_iZDQeUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQekIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQe0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQfEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQfUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQfkIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQf0IZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQgEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQgUIZEfC3to_rLoZHFQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQgkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQg0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQhEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQhUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQhkIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQh0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQiEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQiUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQikIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQi0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_iZDT8kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQjEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQjUIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_iZDT8UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQjkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_iZDT-kIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQj0IZEfC3to_rLoZHFQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_iZDQkEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQkUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQkkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQk0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQlEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQlUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQlkIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQl0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQmEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQmUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQmkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQm0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQnEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQnUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQnkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQn0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQoEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQoUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQokIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQo0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_iZDUA0IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQpEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_iZDQpUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQpkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQp0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQqEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQqUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQqkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_iZDT6UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQq0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQrEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQrUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQrkIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQr0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQsEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQsUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQskIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_iZDT-EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQs0IZEfC3to_rLoZHFQ" elementId="url" name="URL"/>
    <parameters xmi:id="_iZDQtEIZEfC3to_rLoZHFQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_iZDQtUIZEfC3to_rLoZHFQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_iZDQtkIZEfC3to_rLoZHFQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_iZDQt0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQuEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQuUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQukIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQu0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQvEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_iZDT-EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQvUIZEfC3to_rLoZHFQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_iZDQvkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQv0IZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQwEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQwUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQwkIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQw0IZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQxEIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="_iZDT5EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQxUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQxkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQx0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQyEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQyUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQykIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQy0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQzEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQzUIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_iZDT5UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQzkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQz0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ0EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ0UIZEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_iZDT8EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ0kIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ00IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ1EIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="_iZDT50IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ1UIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ1kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ10IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ2EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ2UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ2kIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ20IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ3EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ3UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ3kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ30IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ4EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ4UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ4kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ40IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ5EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ5UIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ5kIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_iZDT6UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ50IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ6EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ6UIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ6kIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ60IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ7EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_iZDT9EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDQ7UIZEfC3to_rLoZHFQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_iZDQ7kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ70IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ8EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_iZDUBEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ8UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ8kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ80IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ9EIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ9UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ9kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ90IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ-EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ-UIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ-kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ-0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ_EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ_UIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ_kIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDQ_0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRAEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRAUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRAkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRA0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRBEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRBkIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="_iZDT-0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRB0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRCEIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_iZDT_0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRCUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRCkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRC0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_iZDT70IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRDEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_iZDRDUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRDkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRD0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDREEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDREUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDREkIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRE0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRFEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRFUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRFkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRF0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRGEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRGUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRGkIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_iZDT4kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRG0IZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRHEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRHUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRHkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRH0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRIEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRIUIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRIkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_iZDT9EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRI0IZEfC3to_rLoZHFQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_iZDRJEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRJUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRJkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRJ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRKEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRKUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRKkIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRK0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRLEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRLUIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRLkIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRL0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRMEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRMUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRMkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRM0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_iZDUBUIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRNEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_iZDRNUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_iZDRNkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRN0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDROEIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDROUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDROkIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRO0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRPEIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRPUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRPkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRP0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRQEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRQUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRQkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRQ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRREIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRRUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRRkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRR0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_iZDUB0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRSEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRSUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRSkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRS0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRTEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_iZDT-EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRTUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_iZDRTkIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRT0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRUEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_iZDT-kIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRUUIZEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_iZDRUkIZEfC3to_rLoZHFQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_iZDRU0IZEfC3to_rLoZHFQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_iZDRVEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRVUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRVkIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRV0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRWEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRWUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRWkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRW0IZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRXEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRXUIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRXkIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRX0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRYEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRYUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRYkIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRY0IZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_iZDT9UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRZEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRZUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRZkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRZ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRaEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRaUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRakIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_iZDRa0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRbEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRbUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_iZDT70IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRbkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_iZDRb0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRcEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRcUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_iZDUAkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRckIZEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_iZDRc0IZEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_iZDRdEIZEfC3to_rLoZHFQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_iZDRdUIZEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_iZDRdkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRd0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDReEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDReUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRekIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRe0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRfEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRfUIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRfkIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRf0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRgEIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRgUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRgkIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRg0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRhEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRhUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRhkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRh0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRiEIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_iZDT6kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRiUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRikIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRi0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRjEIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_iZDT9UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRjUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_iZDT-EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRjkIZEfC3to_rLoZHFQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_iZDRj0IZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRkEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRkUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRkkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRk0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRlEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRlUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRlkIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRl0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRmEIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="_iZDT-0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRmUIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRmkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRm0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRnEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRnUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRnkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRn0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRoEIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_iZDT_0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRoUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRokIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRo0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRpEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRpUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRpkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRp0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRqEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRqUIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRqkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRq0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRrEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRrUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRrkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRr0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRsEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRsUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRskIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRs0IZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRtEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRtUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRtkIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRt0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRuEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRuUIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="_iZDT50IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRukIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRu0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRvEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRvUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRvkIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRv0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_iZDUA0IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDRwEIZEfC3to_rLoZHFQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_iZDRwUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRwkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRw0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRxEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRxUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRxkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRx0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRyEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRyUIZEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_iZDT90IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRykIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_iZDT5UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRy0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRzEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRzUIZEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRzkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDRz0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR0EIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR0UIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR0kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR00IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR1EIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR1UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR1kIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR10IZEfC3to_rLoZHFQ" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDR2EIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_iZDR2UIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_iZDR2kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR20IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR3EIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR3UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR3kIZEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDR30IZEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_iZDR4EIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR4UIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR4kIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR40IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR5EIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR5UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR5kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR50IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR6EIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR6UIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR6kIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR60IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR7EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR7UIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR7kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR70IZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR8EIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR8UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR8kIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR80IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR9EIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_iZDUBEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR9UIZEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR9kIZEfC3to_rLoZHFQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_iZDT8EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR90IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR-EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_iZDT5kIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDR-UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_iZDR-kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR-0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR_EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR_UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR_kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDR_0IZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="_iZDT50IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSAEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSAUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSAkIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSA0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSBEIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSBkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSB0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSCEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSCUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSCkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSC0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSDEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSDUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSDkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSD0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSEEIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSEUIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSEkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSE0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_iZDUCEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSFEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSFUIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSFkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSF0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSGEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSGUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSGkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSG0IZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="_iZDT50IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSHEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_iZDT40IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSHUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_iZDSHkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_iZDSH0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_iZDSIEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSIUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSIkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_iZDT70IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSI0IZEfC3to_rLoZHFQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_iZDSJEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSJUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSJkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSJ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSKEIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="_iZDT50IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSKUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSKkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_iZDT8UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSK0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSLEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSLUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_iZDT7kIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSLkIZEfC3to_rLoZHFQ" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_iZDSL0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSMEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSMUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_iZDUCUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSMkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSM0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_iZDT-kIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSNEIZEfC3to_rLoZHFQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_iZDSNUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSNkIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSN0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSOEIZEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSOUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSOkIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_iZDSO0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSPEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSPUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSPkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSP0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSQEIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSQUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSQkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_iZDT8UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSQ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSREIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSRUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSRkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSR0IZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSSEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSSUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSSkIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_iZDT9UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSS0IZEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="_iZDT_UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSTEIZEfC3to_rLoZHFQ" elementId="java.execute.workspaceCommand" commandName="Execute Java Command in Workspace" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSTUIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_iZDSTkIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_iZDST0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSUEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSUUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSUkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSU0IZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSVEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSVUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSVkIZEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSV0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSWEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSWUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSWkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSW0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSXEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSXUIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSXkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSX0IZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSYEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSYUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSYkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSY0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSZEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSZUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSZkIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_iZDT_0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSZ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSaEIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSaUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_iZDUBUIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSakIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_iZDSa0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import from a Source Repository" description="Imports a plug-in from a source repository" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSbEIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSbUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSbkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSb0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDScEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDScUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSckIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSc0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSdEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSdUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSdkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSd0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSeEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSeUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSekIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSe0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSfEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSfUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSfkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSf0IZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSgEIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSgUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSgkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSg0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDShEIZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDShUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDShkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSh0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSiEIZEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="_iZDT_UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSiUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSikIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSi0IZEfC3to_rLoZHFQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSjEIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSjUIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSjkIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSj0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSkEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSkUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSkkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_iZDUBEIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSk0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_iZDSlEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_iZDSlUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSlkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSl0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSmEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSmUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSmkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSm0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSnEIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSnUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSnkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSn0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSoEIZEfC3to_rLoZHFQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSoUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSokIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSo0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSpEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSpUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSpkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSp0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSqEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_iZDUBUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSqUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSqkIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSq0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSrEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSrUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSrkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSr0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSsEIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSsUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSskIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSs0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDStEIZEfC3to_rLoZHFQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDStUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDStkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSt0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSuEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSuUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSukIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSu0IZEfC3to_rLoZHFQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSvEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSvUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSvkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSv0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSwEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSwUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSwkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSw0IZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSxEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_iZDUAkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDSxUIZEfC3to_rLoZHFQ" elementId="title" name="Title"/>
    <parameters xmi:id="_iZDSxkIZEfC3to_rLoZHFQ" elementId="message" name="Message"/>
    <parameters xmi:id="_iZDSx0IZEfC3to_rLoZHFQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_iZDSyEIZEfC3to_rLoZHFQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_iZDSyUIZEfC3to_rLoZHFQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_iZDSykIZEfC3to_rLoZHFQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_iZDSy0IZEfC3to_rLoZHFQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_iZDSzEIZEfC3to_rLoZHFQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_iZDSzUIZEfC3to_rLoZHFQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_iZDSzkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDSz0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS0EIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS0UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS0kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS00IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS1EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS1UIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS1kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS10IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS2EIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS2UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_iZDUBEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS2kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS20IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS3EIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS3UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS3kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDS30IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_iZDS4EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_iZDS4UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS4kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS40IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS5EIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS5UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_iZDT70IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDS5kIZEfC3to_rLoZHFQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_iZDS50IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS6EIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_iZDT40IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS6UIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS6kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS60IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS7EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS7UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS7kIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS70IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS8EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS8UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS8kIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_iZDT4kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS80IZEfC3to_rLoZHFQ" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="_iZDS9EIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="_iZDS9UIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="_iZDS9kIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_iZDT_EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS90IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS-EIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS-UIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS-kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS-0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS_EIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_iZDT_0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS_UIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS_kIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDS_0IZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTAEIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTAUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTAkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTA0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTBEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTBkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTB0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTCEIZEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTCUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTCkIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTC0IZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTDEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTDUIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_iZDT9UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTDkIZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTD0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTEEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTEUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTEkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTE0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTFEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTFUIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTFkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTF0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTGEIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTGUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTGkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTG0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_iZDT7kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTHEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTHUIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_iZDT6kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTHkIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTH0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTIEIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTIUIZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_iZDT80IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTIkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTI0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTJEIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTJUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTJkIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTJ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTKEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_iZDT8UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTKUIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDTKkIZEfC3to_rLoZHFQ" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_iZDTK0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTLEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_iZDT-EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDTLUIZEfC3to_rLoZHFQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_iZDTLkIZEfC3to_rLoZHFQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_iZDTL0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_iZDT-kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTMEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_iZDT9EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTMUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTMkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTM0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTNEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTNUIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTNkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTN0IZEfC3to_rLoZHFQ" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTOEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTOUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_iZDT9EIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDTOkIZEfC3to_rLoZHFQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_iZDTO0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTPEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTPUIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTPkIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTP0IZEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTQEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTQUIZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTQkIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_iZDT7UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTQ0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTREIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTRUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_iZDUAEIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTRkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTR0IZEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDTSEIZEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_iZDTSUIZEfC3to_rLoZHFQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_iZDTSkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_iZDT70IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTS0IZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTTEIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_iZDT-UIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTTUIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_iZDUBkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTTkIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_iZDT9kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTT0IZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTUEIZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="_iZDUA0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTUUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTUkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTU0IZEfC3to_rLoZHFQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTVEIZEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="_iZDUAUIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTVUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_iZDT7EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTVkIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_iZDT5kIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTV0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTWEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTWUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_iZDT70IZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDTWkIZEfC3to_rLoZHFQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_iZDTW0IZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="_iZDUCkIZEfC3to_rLoZHFQ">
    <parameters xmi:id="_iZDTXEIZEfC3to_rLoZHFQ" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="_iZDTXUIZEfC3to_rLoZHFQ" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="_iZDTXkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.arrangeWindowsInFront" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.arrangeWindows.name" description="%command.arrangeWindows.desc" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTX0IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.minimizeWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.minimize.name" description="%command.minimize.desc" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTYEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.zoomWindow" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.zoom.name" description="%command.zoom.desc" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTYUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.cocoa.closeDialog" contributorURI="platform:/fragment/org.eclipse.e4.ui.workbench.renderers.swt.cocoa" commandName="%command.closeDialog.name" description="%command.closeDialog.desc" category="_iZDT-EIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTYkIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_iZDTY0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTZEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTZUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTZkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTZ0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTaEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTaUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTakIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTa0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTbEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTbUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTbkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTb0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTcEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTcUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTckIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTc0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTdEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTdUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTdkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTd0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTeEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTeUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTekIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTe0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTfEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTfUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTfkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTf0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTgEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTgUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTgkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTg0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDThEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDThUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDThkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTh0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTiEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTiUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTikIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTi0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTjEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTjUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTjkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTj0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTkEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTkUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTkkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTk0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTlEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTlUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTlkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTl0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTmEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTmUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTmkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTm0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTnEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTnUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTnkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTn0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDToEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDToUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTokIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTo0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTpEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTpUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTpkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTp0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTqEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTqUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTqkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTq0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTrEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTrUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTrkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTr0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTsEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTsUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTskIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTs0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTtEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTtUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTtkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTt0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTuEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTuUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTukIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTu0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTvEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTvUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTvkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTv0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTwEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTwUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTwkIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTw0IZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTxEIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTxUIZEfC3to_rLoZHFQ" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTxkIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelStartup" commandName="JRebel Configuration Startup Page" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTx0IZEfC3to_rLoZHFQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_iZDT40IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTyEIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebel" commandName="JRebel Configuration" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTyUIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelProjects" commandName="JRebel Configuration Projects Page" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTykIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.openSetupGuide" commandName="JRebel Setup Guide" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTy0IZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebel" commandName="Activate JRebel" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTzEIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTzUIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelRemoteServers" commandName="JRebel Configuration Remote Servers Page" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTzkIZEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_iZDUC0IZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDTz0IZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.jrebelSupportPopup" commandName="JRebel Support" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDT0EIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.configureJRebelAdvanced" commandName="JRebel Configuration Advanced Page" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDT0UIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.remoting.synchronize" commandName="Synchronize" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDT0kIZEfC3to_rLoZHFQ" elementId="org.zeroturnaround.eclipse.commands.activateJRebelPopup" commandName="Activate JRebel" category="_iZDUCkIZEfC3to_rLoZHFQ"/>
  <commands xmi:id="_iZDT00IZEfC3to_rLoZHFQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_iZDUC0IZEfC3to_rLoZHFQ"/>
  <addons xmi:id="_iZDT1EIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_iZDT1UIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_iZDT1kIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_iZDT10IZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_iZDT2EIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_iZDT2UIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_iZDT2kIZEfC3to_rLoZHFQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_iZDT20IZEfC3to_rLoZHFQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_iZDT3EIZEfC3to_rLoZHFQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_iZDT3UIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_iZDT3kIZEfC3to_rLoZHFQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_iZDT30IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_iZDT4EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_iZDT4UIZEfC3to_rLoZHFQ" elementId="org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.cocoa.CocoaUIHandler"/>
  <categories xmi:id="_iZDT4kIZEfC3to_rLoZHFQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_iZDT40IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_iZDT5EIZEfC3to_rLoZHFQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="_iZDT5UIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_iZDT5kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_iZDT50IZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="_iZDT6EIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_iZDT6UIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_iZDT6kIZEfC3to_rLoZHFQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_iZDT60IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_iZDT7EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_iZDT7UIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_iZDT7kIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_iZDT70IZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_iZDT8EIZEfC3to_rLoZHFQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_iZDT8UIZEfC3to_rLoZHFQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_iZDT8kIZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_iZDT80IZEfC3to_rLoZHFQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_iZDT9EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_iZDT9UIZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_iZDT9kIZEfC3to_rLoZHFQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_iZDT90IZEfC3to_rLoZHFQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_iZDT-EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_iZDT-UIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_iZDT-kIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_iZDT-0IZEfC3to_rLoZHFQ" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="_iZDT_EIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_iZDT_UIZEfC3to_rLoZHFQ" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="_iZDT_kIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_iZDT_0IZEfC3to_rLoZHFQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_iZDUAEIZEfC3to_rLoZHFQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_iZDUAUIZEfC3to_rLoZHFQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_iZDUAkIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_iZDUA0IZEfC3to_rLoZHFQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_iZDUBEIZEfC3to_rLoZHFQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_iZDUBUIZEfC3to_rLoZHFQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_iZDUBkIZEfC3to_rLoZHFQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_iZDUB0IZEfC3to_rLoZHFQ" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_iZDUCEIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_iZDUCUIZEfC3to_rLoZHFQ" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_iZDUCkIZEfC3to_rLoZHFQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_iZDUC0IZEfC3to_rLoZHFQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
</application:Application>
