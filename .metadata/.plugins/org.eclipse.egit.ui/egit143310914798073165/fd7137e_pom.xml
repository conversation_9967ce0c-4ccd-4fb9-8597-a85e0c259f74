<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.2.12</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>tr.gov.tubitak.mavp</groupId>
	<artifactId>market-indexer</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>market-indexer</name>
	<description>market-indexer</description>
	<properties>
		<java.version>21</java.version>
		<solrj.version>9.6.0</solrj.version>
		<apache.common.lang3.version>3.14.0</apache.common.lang3.version>
		<common.io.version>2.16.1</common.io.version>
		<common.io.version>2.16.1</common.io.version>
		<spring-integration-sftp.version>6.2.4</spring-integration-sftp.version>
		<spring-integration-core.version>6.2.4</spring-integration-core.version>
		<sentry.version>7.9.0</sentry.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.solr</groupId>
			<artifactId>solr-solrj</artifactId>
			<version>${solrj.version}</version>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-client</artifactId>
			<version>10.0.20</version>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-io</artifactId>
			<version>10.0.20</version>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-util</artifactId>
			<version>10.0.20</version>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty.http2</groupId>
			<artifactId>http2-http-client-transport</artifactId>
			<version>10.0.20</version>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-alpn-java-client</artifactId>
			<version>10.0.20</version>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-http</artifactId>
			<version>10.0.20</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>${apache.common.lang3.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${common.io.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-sftp</artifactId>
			<version>${spring-integration-sftp.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-core</artifactId>
			<version>${spring-integration-core.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>
		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version> <!-- veya en son sürüm -->
		</dependency>
		<dependency>
			<groupId>io.sentry</groupId>
			<artifactId>sentry-spring-boot-starter</artifactId>
			<version>${sentry.version}</version>
		</dependency>
		<dependency>
			<groupId>zemberek-nlp</groupId>
			<artifactId>zemberek-morphology</artifactId>
			<version>0.17.1</version>
		</dependency>
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>5.9</version>
		</dependency>
		<dependency>
			<groupId>zemberek-nlp</groupId>
			<artifactId>zemberek-core</artifactId>
			<version>0.17.1</version>
		</dependency>
		<dependency>
			<groupId>zemberek-nlp</groupId>
			<artifactId>zemberek-tokenization</artifactId>
			<version>0.17.1</version>
		</dependency>
		<dependency>
			<groupId>zemberek-nlp</groupId>
			<artifactId>zemberek-apps</artifactId>
			<version>0.17.1</version>
		</dependency>
		<dependency>
			<groupId>zemberek-nlp</groupId>
			<artifactId>zemberek-classification</artifactId>
			<version>0.17.1</version>
		</dependency>
		<dependency>
			<groupId>zemberek-nlp</groupId>
			<artifactId>zemberek-normalization</artifactId>
			<version>0.17.1</version>
		</dependency>
		<dependency>
			<groupId>zemberek-nlp</groupId>
			<artifactId>zemberek-ner</artifactId>
			<version>0.17.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-core</artifactId>
			<version>8.11.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-analyzers-common</artifactId>
			<version>8.11.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-queries</artifactId>
			<version>8.11.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-math3</artifactId>
			<version>3.6.1</version>
		</dependency>
		<dependency>
			<groupId>com.hazelcast</groupId>
			<artifactId>hazelcast</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
		<dependency>
		    <groupId>org.apache.poi</groupId>
		    <artifactId>poi-ooxml</artifactId>
		    <version>5.3.0</version>
		</dependency>


	</dependencies>

	<repositories>
		<repository>
			<id>ahmetaa-repo</id>
			<name>ahmetaa Maven Repo on Github</name>
			<url>https://raw.github.com/ahmetaa/maven-repo/master</url>
		</repository>
	</repositories>


	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
