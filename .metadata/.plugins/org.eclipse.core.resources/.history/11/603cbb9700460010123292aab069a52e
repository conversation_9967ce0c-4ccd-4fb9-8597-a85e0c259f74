# a docker-compose.yml to start a simple cluster with three ZooKeeper nodes and three Solr nodes.
#
# To use:
#
# mkdir mycluster
# cd mycluster
# curl --output docker-compose.yml https://raw.githubusercontent.com/docker-solr/docker-solr-examples/master/docker-compose/docker-compose.yml
# docker-compose up
#
version: '3.7'
services:
  solr1:
    image: solr:9.8.1
    container_name: solr1
    ports:
     - "8981:8981"
    # Start as root to set permissions, then switch to solr user
    user: root
    environment:
      - ZK_HOST=zoo1:2181
      - SOLR_HOST=***********
      - SOLR_JETTY_HOST=0.0.0.0
      - SOLR_PORT=8981
      - SOLR_JAVA_MEM=-Xms10g -Xmx10g
      - SOLR_OPTS=-XX:+UseG1GC -XX:-UseLargePages -Dsolr.query.maxBooleanClauses=100000 -Dsolr.filterCache.size=20480 -Dsolr.queryResultCache.size=2048 -Dsolr.facetCache.size=1024 -Dsolr.exitOnOutOfMemoryError=false -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/solr/logs/heapdump.hprof -Xlog:gc*:file=/var/solr/logs/gc.log:time,uptime:filecount=5,filesize=20M -Xlog:gc+heap=debug:file=/var/solr/logs/gc-heap.log:time,uptime:filecount=5,filesize=20M -Xlog:safepoint:file=/var/solr/logs/safepoint.log:time,uptime:filecount=5,filesize=20M -XX:-CrashOnOutOfMemoryError -Dsolr.json.facet.blockChildren.cache=true -Dsolr.config.lib.enabled=true

    networks:
      - solr
    volumes:
      # Solr 9.8.1 Security-Compliant Custom Library Loading
      # Mount shared libraries to the Solr installation lib directory (not uploaded to ZooKeeper)
      - ./shared-lib:/opt/solr/server/solr/lib

      # Mount configuration files to the proper Solr 9.8.1 locations
      - ./data/conf:/opt/solr/server/solr/configsets/_default/conf
      - ./data/solr.xml:/var/solr/data/solr.xml

      # Mount custom log4j2.xml configuration
      - ./data/conf/log4j2.xml:/opt/solr/server/resources/log4j2.xml

      # Mount data directory for persistence
      - solr_data:/var/solr/data

    # Enhanced command for Solr 9.8.1 security model
    command: >
      bash -c '
        echo "=== Solr 9.8.1 Custom Library Setup ==="

        # Create necessary directories with proper permissions
        mkdir -p /opt/solr/server/solr/lib
        mkdir -p /var/solr/data
        mkdir -p /var/solr/logs

        # Set ownership for all Solr directories
        chown -R solr:solr /var/solr
        chown -R solr:solr /opt/solr/server/solr/lib

        # Verify custom libraries are accessible
        echo "Custom libraries found:"
        ls -la /opt/solr/server/solr/lib/ || echo "No custom libraries directory found"

        # Set proper permissions for custom libraries
        if [ -d "/opt/solr/server/solr/lib" ]; then
          chmod -R 644 /opt/solr/server/solr/lib/*.jar 2>/dev/null || true
          chown -R solr:solr /opt/solr/server/solr/lib
          echo "Custom library permissions set successfully"
        fi

        # Verify configuration files
        echo "Configuration files:"
        ls -la /opt/solr/server/solr/configsets/_default/conf/ | head -5

        echo "=== Starting Solr 9.8.1 ==="
        # Switch to solr user and start Solr
        exec gosu solr solr-foreground
      '
    depends_on:
      - zoo1

  zoo1:
    image: zookeeper:3.9.2
    container_name: zoo1
    restart: always
    hostname: zoo1
    ports:
      - 2181:2181
      - 7001:7000
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181
      ZOO_4LW_COMMANDS_WHITELIST: mntr, conf, ruok
      ZOO_CFG_EXTRA: "metricsProvider.className=org.apache.zookeeper.metrics.prometheus.PrometheusMetricsProvider metricsProvider.httpPort=7000 metricsProvider.exportJvmInfo=true"
    networks:
      - solr



networks:
  solr:

volumes:
  solr_data:
