# a docker-compose.yml to start a simple cluster with three ZooKeeper nodes and three Solr nodes.
#
# To use:
#
# mkdir mycluster
# cd mycluster
# curl --output docker-compose.yml https://raw.githubusercontent.com/docker-solr/docker-solr-examples/master/docker-compose/docker-compose.yml
# docker-compose up
#
version: '3.7'
services:
  solr1:
    image: solr:9.8.1
    container_name: solr1
    ports:
     - "8981:8981"
    # Start as root to set permissions, then switch to solr user
    user: root
    environment:
      - ZK_HOST=zoo1:2181
      - SOLR_HOST=***********
      - SOLR_JETTY_HOST=0.0.0.0
      - SOLR_PORT=8981
      - SOLR_JAVA_MEM=-Xms10g -Xmx10g
      - SOLR_OPTS=-XX:+UseG1GC -XX:-UseLargePages -Dsolr.query.maxBooleanClauses=100000 -Dsolr.filterCache.size=20480 -Dsolr.queryResultCache.size=2048 -Dsolr.exitOnOutOfMemoryError=false -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/solr/logs/heapdump.hprof -Xlog:gc*:file=/var/solr/logs/gc.log:time,uptime:filecount=5,filesize=20M -Xlog:gc+heap=debug:file=/var/solr/logs/gc-heap.log:time,uptime:filecount=5,filesize=20M -Xlog:safepoint:file=/var/solr/logs/safepoint.log:time,uptime:filecount=5,filesize=20M -XX:-CrashOnOutOfMemoryError -Dsolr.json.facet.blockChildren.cache=true

    networks:
      - solr
    volumes:
      # Mount the lib directory to multiple locations to ensure the JARs are found (updated paths for 9.8.1)
      - ./data/lib:/opt/solr-9.8.1/server/solr/lib
      - ./data/lib:/opt/solr-9.8.1/contrib/analysis-extras/lib
      - ./data/lib:/opt/solr-9.8.1/modules/analysis-extras/lib
      # Mount the configuration files
      - ./data/conf:/opt/solr-9.8.1/server/solr/configsets/_default/conf
      - ./data/solr.xml:/opt/solr-9.8.1/server/solr/solr.xml
      # Mount the custom log4j2.xml configuration
      - ./data/conf/log4j2.xml:/opt/solr-9.8.1/server/resources/log4j2.xml
    # Command to set permissions and then start Solr as the solr user
    command: >
      bash -c '
        # Create directories if they don't exist
        mkdir -p /opt/solr-9.8.1/server/solr/lib
        mkdir -p /opt/solr-9.8.1/contrib/analysis-extras/lib
        mkdir -p /opt/solr-9.8.1/modules/analysis-extras/lib

        # Set permissions
        chown -R solr:solr /opt/solr-9.8.1/server/solr/lib
        chown -R solr:solr /opt/solr-9.8.1/contrib/analysis-extras/lib
        chown -R solr:solr /opt/solr-9.8.1/modules/analysis-extras/lib

        # Switch to solr user and start Solr
        exec gosu solr solr-foreground
      '
    depends_on:
      - zoo1

  zoo1:
    image: zookeeper:3.9.2
    container_name: zoo1
    restart: always
    hostname: zoo1
    ports:
      - 2181:2181
      - 7001:7000
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181
      ZOO_4LW_COMMANDS_WHITELIST: mntr, conf, ruok
      ZOO_CFG_EXTRA: "metricsProvider.className=org.apache.zookeeper.metrics.prometheus.PrometheusMetricsProvider metricsProvider.httpPort=7000 metricsProvider.exportJvmInfo=true"
    networks:
      - solr



networks:
  solr:
