<?xml version="1.0" encoding="UTF-8" ?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<!--

 This example schema is the recommended starting point for users.
 It should be kept correct and concise, usable out-of-the-box.


 For more information, on how to customize this file, please see
 https://solr.apache.org/guide/solr/latest/indexing-guide/schema-elements.html

 PERFORMANCE NOTE: this schema includes many optional features and should not
 be used for benchmarking.  To improve performance one could
  - set stored="false" for all fields possible (esp large fields) when you
    only need to search on the field but don't need to return the original
    value.
  - set indexed="false" if you don't need to search on the field, but only
    return the field as a result of searching on other indexed fields.
  - remove all unneeded copyField statements
  - for best index size and searching performance, set "index" to false
    for all general text fields, use copyField to copy them to the
    catchall "text" field, and use that for searching.
-->

<schema name="default-config" version="1.6">
    <!-- attribute "name" is the name of this schema and is only used for display purposes.
       version="x.y" is Solr's version number for the schema syntax and
       semantics.  It should not normally be changed by applications.

       1.0: multiValued attribute did not exist, all fields are multiValued
            by nature
       1.1: multiValued attribute introduced, false by default
       1.2: omitTermFreqAndPositions attribute introduced, true by default
            except for text fields.
       1.3: removed optional field compress feature
       1.4: autoGeneratePhraseQueries attribute introduced to drive QueryParser
            behavior when a single string produces multiple tokens.  Defaults
            to off for version >= 1.4
       1.5: omitNorms defaults to true for primitive field types
            (int, float, boolean, string...)
       1.6: useDocValuesAsStored defaults to true.
    -->

    <!-- Valid attributes for fields:
     name: mandatory - the name for the field
     type: mandatory - the name of a field type from the
       fieldTypes section
     indexed: true if this field should be indexed (searchable or sortable)
     stored: true if this field should be retrievable
     docValues: true if this field should have doc values. Doc Values is
       recommended (required, if you are using *Point fields) for faceting,
       grouping, sorting and function queries. Doc Values will make the index
       faster to load, more NRT-friendly and more memory-efficient.
       They are currently only supported by StrField, UUIDField, all
       *PointFields, and depending on the field type, they might require
       the field to be single-valued, be required or have a default value
       (check the documentation of the field type you're interested in for
       more information)
     multiValued: true if this field may contain multiple values per document
     omitNorms: (expert) set to true to omit the norms associated with
       this field (this disables length normalization and index-time
       boosting for the field, and saves some memory).  Only full-text
       fields or fields that need an index-time boost need norms.
       Norms are omitted for primitive (non-analyzed) types by default.
     termVectors: [false] set to true to store the term vector for a
       given field.
       When using MoreLikeThis, fields used for similarity should be
       stored for best performance.
     termPositions: Store position information with the term vector.
       This will increase storage costs.
     termOffsets: Store offset information with the term vector. This
       will increase storage costs.
     required: The field is required.  It will throw an error if the
       value does not exist
     default: a value that should be used if no value is specified
       when adding a document.
    -->

    <!-- field names should consist of alphanumeric or underscore characters only and
      not start with a digit.  This is not currently strictly enforced,
      but other field names will not have first class support from all components
      and back compatibility is not guaranteed.  Names with both leading and
      trailing underscores (e.g. _version_) are reserved.
    -->

    <!-- In this _default configset, only four fields are pre-declared:
         id, _version_, and _text_ and _root_. All other fields will be type guessed and added via the
         "add-unknown-fields-to-the-schema" update request processor chain declared in solrconfig.xml.

         Note that many dynamic fields are also defined - you can use them to specify a
         field's type via field naming conventions - see below.

         WARNING: The _text_ catch-all field will significantly increase your index size.
         If you don't need it, consider removing it and the corresponding copyField directive.
    -->

	<fieldType name="_nest_path_" class="solr.NestPathField" />
	<field name="_nest_path_" type="_nest_path_" />
	<field name="_nest_parent_" type="string" indexed="true" stored="true" />
	
	<field name="depots" type="text_general" multiValued="true" indexed="true" stored="true" />


    <field name="id" type="string" indexed="true" stored="true" required="true" multiValued="false" />
    <!-- docValues are enabled by default for long type so we don't need to index the version field  -->
    <field name="_version_" type="plong" indexed="false" stored="false"/>
    <field name="_root_" type="string" indexed="true" stored="false"/>
    <field name="_text_" type="text_general" indexed="true" stored="false" multiValued="true"/>
    <field name="brand" type="string" docValues="true" indexed="true" stored="true"/>
    <field name="market" type="string" docValues="true" indexed="true" stored="true"/>
    <field name="catch_all" type="text_tr" multiValued="true" indexed="true" stored="false"/>
    <field name="barcodes" type="string" docValues="true" multiValued="true" indexed="true" stored="true"/>
    <field name="image_url" type="string" docValues="true" multiValued="false" indexed="true" stored="true"/>
	<field name="categories" type="text_tr" docValues="false" multiValued="true" indexed="true" stored="true"/>


	<field name="refined_quantity_unit" type="string" docValues="true" multiValued="false" indexed="true" stored="true"/>
	<field name="refined_volume_weight" type="string" docValues="true" multiValued="false" indexed="true" stored="true"/>
	<field name="main_category" type="string_ci" docValues="false" multiValued="false" indexed="true" stored="true"/>
	<field name="sub_category" type="string_ci" docValues="false" multiValued="true" indexed="true" stored="true"/>
	<field name="index_time" type="string" indexed="true" stored="true" multiValued="false" />
	<field name="lowest_price" type="pfloat" indexed="true" stored="true" multiValued="false" />
	<field name="average_price" type="pfloat" indexed="true" stored="true" multiValued="false" />
	<field name="price" type="pfloat" indexed="true" stored="true" multiValued="false" />


	<field name="title_zem" type="text_tr" docValues="false" indexed="true" stored="true"/>
	<field name="title" type="text_tr_optimized" docValues="false" indexed="true" stored="true"/>
	<field name="title_exact" type="text_trci" indexed="true" stored="false"  />
	<field name="title_spellcheck" type="text_tr_spellcheck" indexed="true" stored="true" />

	<!-- Child document fields for offers -->
    <field name="offer_id" type="string" indexed="true" stored="true" />
    <field name="offer_price" type="pfloat" indexed="true" stored="true" />
    <field name="offer_market" type="string" indexed="true" stored="true" />
    <field name="offer_update_date" type="pdate" indexed="true" stored="true" />
    <field name="offer_url" type="string" indexed="true" stored="true" />
    <field name="offer_depot" type="string" indexed="true" stored="true" multiValued="false" />
    <field name="offer_quantity" type="pint" indexed="true" stored="true" />
    <field name="offer_quantity_unit" type="string" indexed="true" stored="true" />
    <field name="offer_volume_weight" type="pfloat" indexed="true" stored="true" />
    <field name="offer_discount" type="boolean" indexed="true" stored="true" default="false" />
    <field name="offer_discount_ratio" type="pfloat" indexed="true" stored="true" />
    <field name="offer_promotion_text" type="string" indexed="true" stored="true" />
    <field name="offer_regular_price" type="pfloat" indexed="true" stored="true" />
    <field name="market_name" type="string_ci" docValues="false" multiValued="false" indexed="true" stored="true"/>


    <!-- This can be enabled, in case the client does not know what fields may be searched. It isn't enabled by default
         because it's very expensive to index everything twice. -->
    <!-- <copyField source="*" dest="_text_"/> -->

    <!-- Dynamic field definitions allow using convention over configuration
       for fields via the specification of patterns to match field names.
       EXAMPLE:  name="*_i" will match any field ending in _i (like myid_i, z_i)
       RESTRICTION: the glob-like pattern in the name attribute must have a "*"
       only at the start or the end.  -->
    <dynamicField name="*__d"  type="pfloat"   docValues="true" indexed="true" multiValued="false" stored="true" />

    <dynamicField name="*_i"   type="pint"     indexed="true"  stored="true"/>
    <dynamicField name="*_is"  type="pints"    indexed="true"  stored="true"/>
    <dynamicField name="*_s"   type="string"   indexed="true"  stored="true" />
    <dynamicField name="*_ss"  type="strings"  indexed="true"  stored="true"/>
    <dynamicField name="*_l"   type="plong"    indexed="true"  stored="true"/>
    <dynamicField name="*_ls"  type="plongs"   indexed="true"  stored="true"/>
    <dynamicField name="*_b"   type="boolean"  indexed="true"  stored="true"/>
    <dynamicField name="*_bs"  type="booleans" indexed="true"  stored="true"/>
    <dynamicField name="*_f"   type="pfloat"   indexed="true"  stored="true"/>
    <dynamicField name="*_fs"  type="pfloats"  indexed="true"  stored="true"/>
    <dynamicField name="*_d"   type="pdouble"  indexed="true"  stored="true"/>
    <dynamicField name="*_ds"  type="pdoubles" indexed="true"  stored="true"/>
    <dynamicField name="*_dt"  type="pdate"    indexed="true"  stored="true"/>
    <dynamicField name="*_dts" type="pdates"   indexed="true"  stored="true"/>
    <dynamicField name="*_t"   type="text_general" indexed="true" stored="true" multiValued="false"/>
    <dynamicField name="*_txt" type="text_general" indexed="true" stored="true"/>

    <dynamicField name="random_*" type="random"/>
    <dynamicField name="ignored_*" type="ignored"/>

    <!-- Type used for data-driven schema, to add a string copy for each text field -->
    <dynamicField name="*_str" type="strings" stored="false" docValues="true" indexed="false" useDocValuesAsStored="false"/>

    <dynamicField name="*_p"  type="location" indexed="true" stored="true"/>
    <dynamicField name="*_srpt"  type="location_rpt" indexed="true" stored="true"/>

    <!-- payloaded dynamic fields -->
    <dynamicField name="*_dpf" type="delimited_payloads_float" indexed="true"  stored="true"/>
    <dynamicField name="*_dpi" type="delimited_payloads_int" indexed="true"  stored="true"/>
    <dynamicField name="*_dps" type="delimited_payloads_string" indexed="true"  stored="true"/>

    <dynamicField name="attr_*" type="text_general" indexed="true" stored="true" multiValued="true"/>

    <!-- Field to use to determine and enforce document uniqueness.
      Unless this field is marked with required="false", it will be a required field
    -->
    <uniqueKey>id</uniqueKey>

    <!-- copyField commands copy one field to another at the time a document
       is added to the index.  It's used either to index the same field differently,
       or to add multiple fields to the same field for easier/faster searching.

    <copyField source="sourceFieldName" dest="destinationFieldName"/>
    -->

    <!-- field type definitions. The "name" attribute is
       just a label to be used by field definitions.  The "class"
       attribute and any other attributes determine the real
       behavior of the fieldType.
         Class names starting with "solr" refer to java classes in a
       standard package such as org.apache.solr.analysis
    -->

    <!-- sortMissingLast and sortMissingFirst attributes are optional attributes are
         currently supported on types that are sorted internally as strings
         and on numeric types.
       This includes "string", "boolean", "pint", "pfloat", "plong", "pdate", "pdouble".
       - If sortMissingLast="true", then a sort on this field will cause documents
         without the field to come after documents with the field,
         regardless of the requested sort order (asc or desc).
       - If sortMissingFirst="true", then a sort on this field will cause documents
         without the field to come before documents with the field,
         regardless of the requested sort order.
       - If sortMissingLast="false" and sortMissingFirst="false" (the default),
         then default lucene sorting will be used which places docs without the
         field first in an ascending sort and last in a descending sort.
    -->

    <!-- The StrField type is not analyzed, but indexed/stored verbatim. -->
    <fieldType name="string" class="solr.StrField" sortMissingLast="true" docValues="true" />
    <fieldType name="strings" class="solr.StrField" sortMissingLast="true" multiValued="true" docValues="true" />

    <!-- boolean type: "true" or "false" -->
    <fieldType name="boolean" class="solr.BoolField" sortMissingLast="true"/>
    <fieldType name="booleans" class="solr.BoolField" sortMissingLast="true" multiValued="true"/>

    <!--
      Numeric field types that index values using KD-trees.
      Point fields don't support FieldCache, so they must have docValues="true" if needed for sorting, faceting, functions, etc.
    -->
    <fieldType name="pint" class="solr.IntPointField" docValues="true"/>
    <fieldType name="pfloat" class="solr.FloatPointField" docValues="true"/>
    <fieldType name="plong" class="solr.LongPointField" docValues="true"/>
    <fieldType name="pdouble" class="solr.DoublePointField" docValues="true"/>

    <fieldType name="pints" class="solr.IntPointField" docValues="true" multiValued="true"/>
    <fieldType name="pfloats" class="solr.FloatPointField" docValues="true" multiValued="true"/>
    <fieldType name="plongs" class="solr.LongPointField" docValues="true" multiValued="true"/>
    <fieldType name="pdoubles" class="solr.DoublePointField" docValues="true" multiValued="true"/>
    <fieldType name="random" class="solr.RandomSortField" indexed="true"/>

    <!-- since fields of this type are by default not stored or indexed,
       any data added to them will be ignored outright.  -->
    <fieldType name="ignored" stored="false" indexed="false" multiValued="true" class="solr.StrField" />

    <!-- The format for this date field is of the form 1995-12-31T23:59:59Z, and
         is a more restricted form of the canonical representation of dateTime
         http://www.w3.org/TR/xmlschema-2/#dateTime
         The trailing "Z" designates UTC time and is mandatory.
         Optional fractional seconds are allowed: 1995-12-31T23:59:59.999Z
         All other components are mandatory.

         Expressions can also be used to denote calculations that should be
         performed relative to "NOW" to determine the value, ie...

               NOW/HOUR
                  ... Round to the start of the current hour
               NOW-1DAY
                  ... Exactly 1 day prior to now
               NOW/DAY+6MONTHS+3DAYS
                  ... 6 months and 3 days in the future from the start of
                      the current day

      -->
    <!-- KD-tree versions of date fields -->
    <fieldType name="pdate" class="solr.DatePointField" docValues="true"/>
    <fieldType name="pdates" class="solr.DatePointField" docValues="true" multiValued="true"/>

    <!--Binary data type. The data should be sent/retrieved in as Base64 encoded Strings -->
    <fieldType name="binary" class="solr.BinaryField"/>

    <!--
    RankFields can be used to store scoring factors to improve document ranking. They should be used
    in combination with RankQParserPlugin.
    (experimental)
    -->
    <fieldType name="rank" class="solr.RankField"/>

    <!-- solr.TextField allows the specification of custom text analyzers
         specified as a tokenizer and a list of token filters. Different
         analyzers may be specified for indexing and querying.

         The optional positionIncrementGap puts space between multiple fields of
         this type on the same document, with the purpose of preventing false phrase
         matching across fields.

         For more info on customizing your analyzer chain, please see
         https://solr.apache.org/guide/solr/latest/indexing-guide/document-analysis.html#using-analyzers-tokenizers-and-filters
     -->

    <!-- One can also specify an existing Analyzer class that has a
         default constructor via the class attribute on the analyzer element.
         Example:
    <fieldType name="text_greek" class="solr.TextField">
      <analyzer class="org.apache.lucene.analysis.el.GreekAnalyzer"/>
    </fieldType>
    -->

    <!-- A text field that only splits on whitespace for exact matching of words -->
    <dynamicField name="*_ws" type="text_ws"  indexed="true"  stored="true"/>
    <fieldType name="text_ws" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="whitespace"/>
      </analyzer>
    </fieldType>

    <!-- A general text field that has reasonable, generic
         cross-language defaults: it tokenizes with StandardTokenizer,
         removes stop words from case-insensitive "stopwords.txt"
         (empty by default), and down cases.  At query time only, it
         also applies synonyms.
    -->
    <fieldType name="text_general" class="solr.TextField" positionIncrementGap="100" multiValued="true">
      <analyzer type="index">
        <tokenizer name="standard"/>
        <filter name="stop" ignoreCase="true" words="stopwords.txt" />
        <!-- in this example, we will only use synonyms at query time
        <filter name="synonymGraph" synonyms="index_synonyms.txt" ignoreCase="true" expand="false"/>
        <filter name="flattenGraph"/>
        -->
        <filter name="lowercase"/>
      </analyzer>
      <analyzer type="query">
        <tokenizer name="standard"/>
        <filter name="stop" ignoreCase="true" words="stopwords.txt" />
        <filter name="synonymGraph" synonyms="synonyms.txt" ignoreCase="true" expand="true"/>
        <filter name="lowercase"/>
      </analyzer>
    </fieldType>


    <!-- SortableTextField generaly functions exactly like TextField,
         except that it supports, and by default uses, docValues for sorting (or faceting)
         on the first 1024 characters of the original field values (which is configurable).

         This makes it a bit more useful then TextField in many situations, but the trade-off
         is that it takes up more space on disk; which is why it's not used in place of TextField
         for every fieldType in this _default schema.
    -->
    <dynamicField name="*_t_sort" type="text_gen_sort" indexed="true" stored="true" multiValued="false"/>
    <dynamicField name="*_txt_sort" type="text_gen_sort" indexed="true" stored="true"/>
    <fieldType name="text_gen_sort" class="solr.SortableTextField" positionIncrementGap="100" multiValued="true">
      <analyzer type="index">
        <tokenizer name="standard"/>
        <filter name="stop" ignoreCase="true" words="stopwords.txt" />
        <filter name="lowercase"/>
      </analyzer>
      <analyzer type="query">
        <tokenizer name="standard"/>
        <filter name="stop" ignoreCase="true" words="stopwords.txt" />
        <filter name="synonymGraph" synonyms="synonyms.txt" ignoreCase="true" expand="true"/>
        <filter name="lowercase"/>
      </analyzer>
    </fieldType>

    <!-- A text field with defaults appropriate for English: it tokenizes with StandardTokenizer,
         removes English stop words (lang/stopwords_en.txt), down cases, protects words from protwords.txt, and
         finally applies Porter's stemming.  The query time analyzer also applies synonyms from synonyms.txt. -->
    <dynamicField name="*_txt_en" type="text_en"  indexed="true"  stored="true"/>
    <fieldType name="text_en" class="solr.TextField" positionIncrementGap="100">
      <analyzer type="index">
        <tokenizer name="standard"/>
        <!-- in this example, we will only use synonyms at query time
        <filter name="synonymGraph" synonyms="index_synonyms.txt" ignoreCase="true" expand="false"/>
        <filter name="flattenGraph"/>
        -->
        <!-- Case insensitive stop word removal.
        -->
        <filter name="stop"
                ignoreCase="true"
                words="lang/stopwords_en.txt"
            />
        <filter name="lowercase"/>
        <filter name="englishPossessive"/>
        <filter name="keywordMarker" protected="protwords.txt"/>
        <!-- Optionally you may want to use this less aggressive stemmer instead of PorterStemFilterFactory:
        <filter name="englishMinimalStem"/>
        -->
        <filter name="porterStem"/>
      </analyzer>
      <analyzer type="query">
        <tokenizer name="standard"/>
        <filter name="synonymGraph" synonyms="synonyms.txt" ignoreCase="true" expand="true"/>
        <filter name="stop"
                ignoreCase="true"
                words="lang/stopwords_en.txt"
        />
        <filter name="lowercase"/>
        <filter name="englishPossessive"/>
        <filter name="keywordMarker" protected="protwords.txt"/>
        <!-- Optionally you may want to use this less aggressive stemmer instead of PorterStemFilterFactory:
        <filter name="englishMinimalStem"/>
        -->
        <filter name="porterStem"/>
      </analyzer>
    </fieldType>

    <!-- A text field with defaults appropriate for English, plus
         aggressive word-splitting and autophrase features enabled.
         This field is just like text_en, except it adds
         WordDelimiterGraphFilter to enable splitting and matching of
         words on case-change, alpha numeric boundaries, and
         non-alphanumeric chars.  This means certain compound word
         cases will work, for example query "wi fi" will match
         document "WiFi" or "wi-fi".
    -->
    <dynamicField name="*_txt_en_split" type="text_en_splitting"  indexed="true"  stored="true"/>
    <fieldType name="text_en_splitting" class="solr.TextField" positionIncrementGap="100" autoGeneratePhraseQueries="true">
      <analyzer type="index">
        <tokenizer name="whitespace"/>
        <!-- in this example, we will only use synonyms at query time
        <filter name="synonymGraph" synonyms="index_synonyms.txt" ignoreCase="true" expand="false"/>
        -->
        <!-- Case insensitive stop word removal.
        -->
        <filter name="stop"
                ignoreCase="true"
                words="lang/stopwords_en.txt"
        />
        <filter name="wordDelimiterGraph" generateWordParts="1" generateNumberParts="1" catenateWords="1" catenateNumbers="1" catenateAll="0" splitOnCaseChange="1"/>
        <filter name="lowercase"/>
        <filter name="keywordMarker" protected="protwords.txt"/>
        <filter name="porterStem"/>
        <filter name="flattenGraph" />
      </analyzer>
      <analyzer type="query">
        <tokenizer name="whitespace"/>
        <filter name="synonymGraph" synonyms="synonyms.txt" ignoreCase="true" expand="true"/>
        <filter name="stop"
                ignoreCase="true"
                words="lang/stopwords_en.txt"
        />
        <filter name="wordDelimiterGraph" generateWordParts="1" generateNumberParts="1" catenateWords="0" catenateNumbers="0" catenateAll="0" splitOnCaseChange="1"/>
        <filter name="lowercase"/>
        <filter name="keywordMarker" protected="protwords.txt"/>
        <filter name="porterStem"/>
      </analyzer>
    </fieldType>

    <!-- Less flexible matching, but less false matches.  Probably not ideal for product names,
         but may be good for SKUs.  Can insert dashes in the wrong place and still match. -->
    <dynamicField name="*_txt_en_split_tight" type="text_en_splitting_tight"  indexed="true"  stored="true"/>
    <fieldType name="text_en_splitting_tight" class="solr.TextField" positionIncrementGap="100" autoGeneratePhraseQueries="true">
      <analyzer type="index">
        <tokenizer name="whitespace"/>
        <filter name="synonymGraph" synonyms="synonyms.txt" ignoreCase="true" expand="false"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_en.txt"/>
        <filter name="wordDelimiterGraph" generateWordParts="0" generateNumberParts="0" catenateWords="1" catenateNumbers="1" catenateAll="0"/>
        <filter name="lowercase"/>
        <filter name="keywordMarker" protected="protwords.txt"/>
        <filter name="englishMinimalStem"/>
        <!-- this filter can remove any duplicate tokens that appear at the same position - sometimes
             possible with WordDelimiterGraphFilter in conjuncton with stemming. -->
        <filter name="removeDuplicates"/>
        <filter name="flattenGraph" />
      </analyzer>
      <analyzer type="query">
        <tokenizer name="whitespace"/>
        <filter name="synonymGraph" synonyms="synonyms.txt" ignoreCase="true" expand="false"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_en.txt"/>
        <filter name="wordDelimiterGraph" generateWordParts="0" generateNumberParts="0" catenateWords="1" catenateNumbers="1" catenateAll="0"/>
        <filter name="lowercase"/>
        <filter name="keywordMarker" protected="protwords.txt"/>
        <filter name="englishMinimalStem"/>
        <!-- this filter can remove any duplicate tokens that appear at the same position - sometimes
             possible with WordDelimiterGraphFilter in conjuncton with stemming. -->
        <filter name="removeDuplicates"/>
      </analyzer>
    </fieldType>

    <!-- Just like text_general except it reverses the characters of
         each token, to enable more efficient leading wildcard queries.
    -->
    <dynamicField name="*_txt_rev" type="text_general_rev"  indexed="true"  stored="true"/>
    <fieldType name="text_general_rev" class="solr.TextField" positionIncrementGap="100">
      <analyzer type="index">
        <tokenizer name="standard"/>
        <filter name="stop" ignoreCase="true" words="stopwords.txt" />
        <filter name="lowercase"/>
        <filter name="reversedWildcard" withOriginal="true"
                maxPosAsterisk="3" maxPosQuestion="2" maxFractionAsterisk="0.33"/>
      </analyzer>
      <analyzer type="query">
        <tokenizer name="standard"/>
        <filter name="synonymGraph" synonyms="synonyms.txt" ignoreCase="true" expand="true"/>
        <filter name="stop" ignoreCase="true" words="stopwords.txt" />
        <filter name="lowercase"/>
      </analyzer>
    </fieldType>

    <dynamicField name="*_phon_en" type="phonetic_en"  indexed="true"  stored="true"/>
    <fieldType name="phonetic_en" stored="false" indexed="true" class="solr.TextField" >
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="doubleMetaphone" inject="false"/>
      </analyzer>
    </fieldType>

    <!-- lowercases the entire field value, keeping it as a single token.  -->
    <dynamicField name="*_s_lower" type="lowercase"  indexed="true"  stored="true"/>
    <fieldType name="lowercase" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="keyword"/>
        <filter name="lowercase" />
      </analyzer>
    </fieldType>

    <!--
      Example of using PathHierarchyTokenizerFactory at index time, so
      queries for paths match documents at that path, or in descendent paths
    -->
    <dynamicField name="*_descendent_path" type="descendent_path"  indexed="true"  stored="true"/>
    <fieldType name="descendent_path" class="solr.TextField">
      <analyzer type="index">
        <tokenizer name="pathHierarchy" delimiter="/" />
      </analyzer>
      <analyzer type="query">
        <tokenizer name="keyword" />
      </analyzer>
    </fieldType>

    <!--
      Example of using PathHierarchyTokenizerFactory at query time, so
      queries for paths match documents at that path, or in ancestor paths
    -->
    <dynamicField name="*_ancestor_path" type="ancestor_path"  indexed="true"  stored="true"/>
    <fieldType name="ancestor_path" class="solr.TextField">
      <analyzer type="index">
        <tokenizer name="keyword" />
      </analyzer>
      <analyzer type="query">
        <tokenizer name="pathHierarchy" delimiter="/" />
      </analyzer>
    </fieldType>

    <!-- This point type indexes the coordinates as separate fields (subFields)
      If subFieldType is defined, it references a type, and a dynamic field
      definition is created matching *___<typename>.  Alternately, if
      subFieldSuffix is defined, that is used to create the subFields.
      Example: if subFieldType="double", then the coordinates would be
        indexed in fields myloc_0___double,myloc_1___double.
      Example: if subFieldSuffix="_d" then the coordinates would be indexed
        in fields myloc_0_d,myloc_1_d
      The subFields are an implementation detail of the fieldType, and end
      users normally should not need to know about them.
     -->
    <dynamicField name="*_point" type="point"  indexed="true"  stored="true"/>
    <fieldType name="point" class="solr.PointType" dimension="2" subFieldSuffix="_d"/>

    <!-- A specialized field for geospatial search filters and distance sorting. -->
    <fieldType name="location" class="solr.LatLonPointSpatialField" docValues="true"/>

    <!-- A geospatial field type that supports multiValued and polygon shapes.
      For more information about this and other spatial fields see:
      https://solr.apache.org/guide/solr/latest/query-guide/spatial-search.html
    -->
    <fieldType name="location_rpt" class="solr.SpatialRecursivePrefixTreeFieldType"
               geo="true" distErrPct="0.025" maxDistErr="0.001" distanceUnits="kilometers" />

    <!-- Payloaded field types -->
    <fieldType name="delimited_payloads_float" stored="false" indexed="true" class="solr.TextField">
      <analyzer>
        <tokenizer name="whitespace"/>
        <filter name="delimitedPayload" encoder="float"/>
      </analyzer>
    </fieldType>
    <fieldType name="delimited_payloads_int" stored="false" indexed="true" class="solr.TextField">
      <analyzer>
        <tokenizer name="whitespace"/>
        <filter name="delimitedPayload" encoder="integer"/>
      </analyzer>
    </fieldType>
    <fieldType name="delimited_payloads_string" stored="false" indexed="true" class="solr.TextField">
      <analyzer>
        <tokenizer name="whitespace"/>
        <filter name="delimitedPayload" encoder="identity"/>
      </analyzer>
    </fieldType>

    <!-- some examples for different languages (generally ordered by ISO code) -->

    <!-- Arabic -->
    <dynamicField name="*_txt_ar" type="text_ar"  indexed="true"  stored="true"/>
    <fieldType name="text_ar" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <!-- for any non-arabic -->
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_ar.txt" />
        <!-- normalizes ﻯ to ﻱ, etc -->
        <filter name="arabicNormalization"/>
        <filter name="arabicStem"/>
      </analyzer>
    </fieldType>

    <!-- Bulgarian -->
    <dynamicField name="*_txt_bg" type="text_bg"  indexed="true"  stored="true"/>
    <fieldType name="text_bg" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_bg.txt" />
        <filter name="bulgarianStem"/>
      </analyzer>
    </fieldType>

    <!-- Catalan -->
    <dynamicField name="*_txt_ca" type="text_ca"  indexed="true"  stored="true"/>
    <fieldType name="text_ca" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <!-- removes l', etc -->
        <filter name="elision" ignoreCase="true" articles="lang/contractions_ca.txt"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_ca.txt" />
        <filter name="snowballPorter" language="Catalan"/>
      </analyzer>
    </fieldType>

    <!-- CJK bigram (see text_ja for a Japanese configuration using morphological analysis) -->
    <dynamicField name="*_txt_cjk" type="text_cjk"  indexed="true"  stored="true"/>
    <fieldType name="text_cjk" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <!-- normalize width before bigram, as e.g. half-width dakuten combine  -->
        <filter name="CJKWidth"/>
        <!-- for any non-CJK -->
        <filter name="lowercase"/>
        <filter name="CJKBigram"/>
      </analyzer>
    </fieldType>

    <!-- Czech -->
    <dynamicField name="*_txt_cz" type="text_cz"  indexed="true"  stored="true"/>
    <fieldType name="text_cz" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_cz.txt" />
        <filter name="czechStem"/>
      </analyzer>
    </fieldType>

    <!-- Danish -->
    <dynamicField name="*_txt_da" type="text_da"  indexed="true"  stored="true"/>
    <fieldType name="text_da" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_da.txt" format="snowball" />
        <filter name="snowballPorter" language="Danish"/>
      </analyzer>
    </fieldType>

    <!-- German -->
    <dynamicField name="*_txt_de" type="text_de"  indexed="true"  stored="true"/>
    <fieldType name="text_de" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_de.txt" format="snowball" />
        <filter name="germanNormalization"/>
        <filter name="germanLightStem"/>
        <!-- less aggressive: <filter name="germanMinimalStem"/> -->
        <!-- more aggressive: <filter name="snowballPorter" language="German2"/> -->
      </analyzer>
    </fieldType>

    <!-- Greek -->
    <dynamicField name="*_txt_el" type="text_el"  indexed="true"  stored="true"/>
    <fieldType name="text_el" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <!-- greek specific lowercase for sigma -->
        <filter name="greekLowercase"/>
        <filter name="stop" ignoreCase="false" words="lang/stopwords_el.txt" />
        <filter name="greekStem"/>
      </analyzer>
    </fieldType>

    <!-- Spanish -->
    <dynamicField name="*_txt_es" type="text_es"  indexed="true"  stored="true"/>
    <fieldType name="text_es" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_es.txt" format="snowball" />
        <filter name="spanishLightStem"/>
        <!-- more aggressive: <filter name="snowballPorter" language="Spanish"/> -->
      </analyzer>
    </fieldType>

    <!-- Estonian -->
    <dynamicField name="*_txt_et" type="text_et"  indexed="true"  stored="true"/>
    <fieldType name="text_et" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_et.txt" />
        <filter name="snowballPorter" language="Estonian"/>
      </analyzer>
    </fieldType>

    <!-- Basque -->
    <dynamicField name="*_txt_eu" type="text_eu"  indexed="true"  stored="true"/>
    <fieldType name="text_eu" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_eu.txt" />
        <filter name="snowballPorter" language="Basque"/>
      </analyzer>
    </fieldType>

    <!-- Persian -->
    <dynamicField name="*_txt_fa" type="text_fa"  indexed="true"  stored="true"/>
    <fieldType name="text_fa" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <!-- for ZWNJ -->
        <charFilter name="persian"/>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="arabicNormalization"/>
        <filter name="persianNormalization"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_fa.txt" />
      </analyzer>
    </fieldType>

    <!-- Finnish -->
    <dynamicField name="*_txt_fi" type="text_fi"  indexed="true"  stored="true"/>
    <fieldType name="text_fi" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_fi.txt" format="snowball" />
        <filter name="snowballPorter" language="Finnish"/>
        <!-- less aggressive: <filter name="finnishLightStem"/> -->
      </analyzer>
    </fieldType>

    <!-- French -->
    <dynamicField name="*_txt_fr" type="text_fr"  indexed="true"  stored="true"/>
    <fieldType name="text_fr" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <!-- removes l', etc -->
        <filter name="elision" ignoreCase="true" articles="lang/contractions_fr.txt"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_fr.txt" format="snowball" />
        <filter name="frenchLightStem"/>
        <!-- less aggressive: <filter name="frenchMinimalStem"/> -->
        <!-- more aggressive: <filter name="snowballPorter" language="French"/> -->
      </analyzer>
    </fieldType>

    <!-- Irish -->
    <dynamicField name="*_txt_ga" type="text_ga"  indexed="true"  stored="true"/>
    <fieldType name="text_ga" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <!-- removes d', etc -->
        <filter name="elision" ignoreCase="true" articles="lang/contractions_ga.txt"/>
        <!-- removes n-, etc. position increments is intentionally false! -->
        <filter name="stop" ignoreCase="true" words="lang/hyphenations_ga.txt"/>
        <filter name="irishLowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_ga.txt"/>
        <filter name="snowballPorter" language="Irish"/>
      </analyzer>
    </fieldType>

    <!-- Galician -->
    <dynamicField name="*_txt_gl" type="text_gl"  indexed="true"  stored="true"/>
    <fieldType name="text_gl" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_gl.txt" />
        <filter name="galicianStem"/>
        <!-- less aggressive: <filter name="galicianMinimalStem"/> -->
      </analyzer>
    </fieldType>

    <!-- Hindi -->
    <dynamicField name="*_txt_hi" type="text_hi"  indexed="true"  stored="true"/>
    <fieldType name="text_hi" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <!-- normalizes unicode representation -->
        <filter name="indicNormalization"/>
        <!-- normalizes variation in spelling -->
        <filter name="hindiNormalization"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_hi.txt" />
        <filter name="hindiStem"/>
      </analyzer>
    </fieldType>

    <!-- Hungarian -->
    <dynamicField name="*_txt_hu" type="text_hu"  indexed="true"  stored="true"/>
    <fieldType name="text_hu" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_hu.txt" format="snowball" />
        <filter name="snowballPorter" language="Hungarian"/>
        <!-- less aggressive: <filter name="hungarianLightStem"/> -->
      </analyzer>
    </fieldType>

    <!-- Armenian -->
    <dynamicField name="*_txt_hy" type="text_hy"  indexed="true"  stored="true"/>
    <fieldType name="text_hy" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_hy.txt" />
        <filter name="snowballPorter" language="Armenian"/>
      </analyzer>
    </fieldType>

    <!-- Indonesian -->
    <dynamicField name="*_txt_id" type="text_id"  indexed="true"  stored="true"/>
    <fieldType name="text_id" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_id.txt" />
        <!-- for a less aggressive approach (only inflectional suffixes), set stemDerivational to false -->
        <filter name="indonesianStem" stemDerivational="true"/>
      </analyzer>
    </fieldType>

    <!-- Italian -->
  <dynamicField name="*_txt_it" type="text_it"  indexed="true"  stored="true"/>
  <fieldType name="text_it" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <!-- removes l', etc -->
        <filter name="elision" ignoreCase="true" articles="lang/contractions_it.txt"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_it.txt" format="snowball" />
        <filter name="italianLightStem"/>
        <!-- more aggressive: <filter name="snowballPorter" language="Italian"/> -->
      </analyzer>
    </fieldType>

    <!-- Japanese using morphological analysis (see text_cjk for a configuration using bigramming)

         NOTE: If you want to optimize search for precision, use default operator AND in your request
         handler config (q.op) Use OR if you would like to optimize for recall (default).
    -->
    <dynamicField name="*_txt_ja" type="text_ja"  indexed="true"  stored="true"/>
    <fieldType name="text_ja" class="solr.TextField" positionIncrementGap="100" autoGeneratePhraseQueries="false">
      <analyzer>
        <!-- Kuromoji Japanese morphological analyzer/tokenizer (JapaneseTokenizer)

           Kuromoji has a search mode (default) that does segmentation useful for search.  A heuristic
           is used to segment compounds into its parts and the compound itself is kept as synonym.

           Valid values for attribute mode are:
              normal: regular segmentation
              search: segmentation useful for search with synonyms compounds (default)
            extended: same as search mode, but unigrams unknown words (experimental)

           For some applications it might be good to use search mode for indexing and normal mode for
           queries to reduce recall and prevent parts of compounds from being matched and highlighted.
           Use <analyzer type="index"> and <analyzer type="query"> for this and mode normal in query.

           Kuromoji also has a convenient user dictionary feature that allows overriding the statistical
           model with your own entries for segmentation, part-of-speech tags and readings without a need
           to specify weights.  Notice that user dictionaries have not been subject to extensive testing.

           User dictionary attributes are:
                     userDictionary: user dictionary filename
             userDictionaryEncoding: user dictionary encoding (default is UTF-8)

           See lang/userdict_ja.txt for a sample user dictionary file.

           Punctuation characters are discarded by default.  Use discardPunctuation="false" to keep them.
        -->
        <tokenizer name="japanese" mode="search"/>
        <!--<tokenizer name="japanese" mode="search" userDictionary="lang/userdict_ja.txt"/>-->
        <!-- Reduces inflected verbs and adjectives to their base/dictionary forms (辞書形) -->
        <filter name="japaneseBaseForm"/>
        <!-- Removes tokens with certain part-of-speech tags -->
        <filter name="japanesePartOfSpeechStop" tags="lang/stoptags_ja.txt" />
        <!-- Normalizes full-width romaji to half-width and half-width kana to full-width (Unicode NFKC subset) -->
        <filter name="cjkWidth"/>
        <!-- Removes common tokens typically not useful for search, but have a negative effect on ranking -->
        <filter name="stop" ignoreCase="true" words="lang/stopwords_ja.txt" />
        <!-- Normalizes common katakana spelling variations by removing any last long sound character (U+30FC) -->
        <filter name="japaneseKatakanaStem" minimumLength="4"/>
        <!-- Lower-cases romaji characters -->
        <filter name="lowercase"/>
      </analyzer>
    </fieldType>

    <!-- Korean morphological analysis -->
    <dynamicField name="*_txt_ko" type="text_ko"  indexed="true"  stored="true"/>
    <fieldType name="text_ko" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <!-- Nori Korean morphological analyzer/tokenizer (KoreanTokenizer)
          The Korean (nori) analyzer integrates Lucene nori analysis module into Solr.
          It uses the mecab-ko-dic dictionary to perform morphological analysis of Korean texts.

          This dictionary was built with MeCab, it defines a format for the features adapted
          for the Korean language.

          Nori also has a convenient user dictionary feature that allows overriding the statistical
          model with your own entries for segmentation, part-of-speech tags and readings without a need
          to specify weights. Notice that user dictionaries have not been subject to extensive testing.

          The tokenizer supports multiple schema attributes:
            * userDictionary: User dictionary path.
            * userDictionaryEncoding: User dictionary encoding.
            * decompoundMode: Decompound mode. Either 'none', 'discard', 'mixed'. Default is 'discard'.
            * outputUnknownUnigrams: If true outputs unigrams for unknown words.
        -->
        <tokenizer name="korean" decompoundMode="discard" outputUnknownUnigrams="false"/>
        <!-- Removes some part of speech stuff like EOMI (Pos.E), you can add a parameter 'tags',
          listing the tags to remove. By default it removes:
          E, IC, J, MAG, MAJ, MM, SP, SSC, SSO, SC, SE, XPN, XSA, XSN, XSV, UNA, NA, VSV
          This is basically an equivalent to stemming.
        -->
        <filter name="koreanPartOfSpeechStop" />
        <!-- Replaces term text with the Hangul transcription of Hanja characters, if applicable: -->
        <filter name="koreanReadingForm" />
        <filter name="lowercase" />
      </analyzer>
    </fieldType>

    <!-- Latvian -->
    <dynamicField name="*_txt_lv" type="text_lv"  indexed="true"  stored="true"/>
    <fieldType name="text_lv" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_lv.txt" />
        <filter name="latvianStem"/>
      </analyzer>
    </fieldType>

    <!-- Dutch -->
    <dynamicField name="*_txt_nl" type="text_nl"  indexed="true"  stored="true"/>
    <fieldType name="text_nl" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_nl.txt" format="snowball" />
        <filter name="stemmerOverride" dictionary="lang/stemdict_nl.txt" ignoreCase="false"/>
        <filter name="snowballPorter" language="Dutch"/>
      </analyzer>
    </fieldType>

    <!-- Norwegian -->
    <dynamicField name="*_txt_no" type="text_no"  indexed="true"  stored="true"/>
    <fieldType name="text_no" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_no.txt" format="snowball" />
        <filter name="snowballPorter" language="Norwegian"/>
        <!-- less aggressive: <filter name="norwegianLightStem"/> -->
        <!-- singular/plural: <filter name="norwegianMinimalStem"/> -->
      </analyzer>
    </fieldType>

    <!-- Portuguese -->
  <dynamicField name="*_txt_pt" type="text_pt"  indexed="true"  stored="true"/>
  <fieldType name="text_pt" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_pt.txt" format="snowball" />
        <filter name="portugueseLightStem"/>
        <!-- less aggressive: <filter name="portugueseMinimalStem"/> -->
        <!-- more aggressive: <filter name="snowballPorter" language="Portuguese"/> -->
        <!-- most aggressive: <filter name="portugueseStem"/> -->
      </analyzer>
    </fieldType>

    <!-- Romanian -->
    <dynamicField name="*_txt_ro" type="text_ro"  indexed="true"  stored="true"/>
    <fieldType name="text_ro" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_ro.txt" />
        <filter name="snowballPorter" language="Romanian"/>
      </analyzer>
    </fieldType>

    <!-- Russian -->
    <dynamicField name="*_txt_ru" type="text_ru"  indexed="true"  stored="true"/>
    <fieldType name="text_ru" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_ru.txt" format="snowball" />
        <filter name="snowballPorter" language="Russian"/>
        <!-- less aggressive: <filter name="russianLightStem"/> -->
      </analyzer>
    </fieldType>

    <!-- Swedish -->
    <dynamicField name="*_txt_sv" type="text_sv"  indexed="true"  stored="true"/>
    <fieldType name="text_sv" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="standard"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_sv.txt" format="snowball" />
        <filter name="snowballPorter" language="Swedish"/>
        <!-- less aggressive: <filter name="swedishLightStem"/> -->
      </analyzer>
    </fieldType>

    <!-- Thai -->
    <dynamicField name="*_txt_th" type="text_th"  indexed="true"  stored="true"/>
    <fieldType name="text_th" class="solr.TextField" positionIncrementGap="100">
      <analyzer>
        <tokenizer name="thai"/>
        <filter name="lowercase"/>
        <filter name="stop" ignoreCase="true" words="lang/stopwords_th.txt" />
      </analyzer>
    </fieldType>

    <!-- Turkish -->
    <dynamicField name="*_txt_tr" type="text_tr"  indexed="true"  stored="true"/>



	<fieldType name="text_tr" class="solr.TextField" positionIncrementGap="100">
      <analyzer type="index">
        <tokenizer class="solr.StandardTokenizerFactory"/>
        <filter class="org.apache.lucene.analysis.tr.TurkishDeASCIIfyFilterFactory" preserveOriginal="false"/>
        <filter class="solr.ApostropheFilterFactory"/>
        <filter class="solr.TurkishLowerCaseFilterFactory"/>
        <filter class="org.apache.lucene.analysis.tr.Zemberek3StemFilterFactory" dictionary="master-dictionary.dict,non-tdk.dict,proper.dict" strategy="maxLength"/>
       </analyzer>
       <analyzer type="query">
         <tokenizer class="solr.StandardTokenizerFactory"/>
         <filter class="org.apache.lucene.analysis.tr.TurkishDeASCIIfyFilterFactory" preserveOriginal="false"/>
         <filter class="solr.ApostropheFilterFactory"/>
         <filter class="solr.TurkishLowerCaseFilterFactory"/>
         <filter class="org.apache.lucene.analysis.tr.Zemberek3StemFilterFactory" dictionary="master-dictionary.dict,non-tdk.dict,proper.dict" strategy="maxLength"/>
       </analyzer>
    </fieldType>


	<fieldType name="text_tr_optimized" class="solr.TextField" positionIncrementGap="100" sortMissingLast="true" omitNorms="true">
		<analyzer type="index">
			<tokenizer class="solr.ICUTokenizerFactory" />
			<filter class="solr.ApostropheFilterFactory"/>
			<filter class="solr.TurkishLowerCaseFilterFactory"/>
			<filter class="solr.SnowballPorterFilterFactory" language="Turkish"/>
			<filter class="solr.PatternCaptureGroupFilterFactory" pattern="(.+)" preserveOriginal="true"/>
		</analyzer>
		<analyzer type="query">
			<tokenizer class="solr.ICUTokenizerFactory"/>
			<filter class="solr.ApostropheFilterFactory"/>
			<filter class="solr.TurkishLowerCaseFilterFactory"/>
			<filter class="solr.SnowballPorterFilterFactory" language="Turkish"/>
		</analyzer>

	</fieldType>


	<fieldType name="text_trci" class="solr.TextField" positionIncrementGap="100"   sortMissingLast="true" omitNorms="true">
	  <analyzer type="index">
		    <tokenizer class="solr.StandardTokenizerFactory"/>
			<filter class="solr.TurkishLowerCaseFilterFactory"/>
			<filter class="solr.ShingleFilterFactory" maxShingleSize="2" outputUnigrams="true" tokenSeparator=""/>
			<filter class="solr.StopFilterFactory" ignoreCase="false" words="lang/stopwords_tr.txt" />
        </analyzer>

		<analyzer type="query">
			<tokenizer class="solr.StandardTokenizerFactory"/>
			<filter class="solr.TurkishLowerCaseFilterFactory"/>
			<filter class="solr.StopFilterFactory" ignoreCase="false" words="lang/stopwords_tr.txt" />
		</analyzer>

		<similarity class="solr.BM25SimilarityFactory">
			<str name="b">0.5</str>
		</similarity>

    </fieldType>

	<fieldType name="text_tr_spellcheck" class="solr.TextField" positionIncrementGap="100">
	  <analyzer>
			<tokenizer class="solr.StandardTokenizerFactory"/>
			<filter class="solr.ApostropheFilterFactory"/>
			<filter class="solr.TurkishLowerCaseFilterFactory"/>
			<filter class="solr.StopFilterFactory" ignoreCase="false" words="lang/stopwords_tr.txt" />
	  </analyzer>
	</fieldType>


	<fieldType name="string_ci" class="solr.TextField" positionIncrementGap="100"   sortMissingLast="true" omitNorms="true">
	  <analyzer>
		<tokenizer class="solr.KeywordTokenizerFactory"/>
		<charFilter class="solr.HTMLStripCharFilterFactory"/>
		<filter class="solr.KeywordRepeatFilterFactory" />
		<filter class="solr.RemoveDuplicatesTokenFilterFactory" />
		<filter class="solr.StopFilterFactory" ignoreCase="false" words="lang/stopwords_tr.txt" />
      </analyzer>
    </fieldType>

	<copyField source="title" dest="title_zem"/>
	<copyField source="title" dest="title_exact"/>
	<copyField source="title" dest="title_spellcheck"/>
    <copyField source="brand" dest="catch_all"/>
    <copyField source="categories" dest="catch_all"/>
    <copyField source="title" dest="catch_all"/>


    <!--


	   Similarity is the scoring routine for each document vs. a query.
       A custom Similarity or SimilarityFactory may be specified here, but
       the default is fine for most applications.
       For more info: https://solr.apache.org/guide/solr/latest/indexing-guide/schema-elements.html#similarity
    -->
    <!--
     <similarity class="com.example.solr.CustomSimilarityFactory">
       <str name="paramkey">param value</str>
     </similarity>
    -->

</schema>
