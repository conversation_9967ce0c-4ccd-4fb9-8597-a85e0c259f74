# Understanding the Solr Nested Document Faceting Problem

## The Problem

The initial problem was related to faceting on child documents in Solr 9.6 when using nested document structures. Specifically, when trying to get facet counts for fields in child documents (like `offer_market` and `offer_depot`) while querying parent documents, the facet results were empty or incorrect.

The query was structured like this:
```
http://10.8.133.89:8981/solr/dev_market/select?q=title:Cino&rows=0&json.facet={"offer_market_facet":{"type":"terms","field":"offer_market","domain":{"blockChildren":"_nest_path_:/depots*"},"facet":{"parents":"unique(_root_)"},"excludeTags":["OFFER_MARKET_FILTER_TAG"],"limit":100},"offer_depot_facet":{"type":"terms","field":"offer_depot","domain":{"blockChildren":"_nest_path_:/depots*"},"facet":{"parents":"unique(_root_)"},"excludeTags":["OFFER_DEPOT_FILTER_TAG"],"limit":100}}

```
this is from one of my old project maybe we should go in this direction: sample code
```
  private void addFacetForReturnedArtifactCount(final SolrQuery solrFacetQuery) {
        solrFacetQuery.set("json.facet", "{ \"counts\": { \"type\": \"terms\",      \"field\": \"ulkeAd\",       \"facet\":{ artifactCount: 'sum(returnedArtifactCount)'} } } ");
    }
```


With a JSON facet configuration like:
```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockChildren": "_nest_path_:/depots*"
    },
    "facet": {
      "parents": "unique(_root_)"
    },
    "excludeTags": ["OFFER_MARKET_FILTER_TAG"],
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "blockChildren": "_nest_path_:/depots*"
    },
    "facet": {
      "parents": "unique(_root_)"
    },
    "excludeTags": ["OFFER_DEPOT_FILTER_TAG"],
    "limit": 100
  }
}
```

However, this configuration was not working, and the facet results were empty:
```json
"facets": {
  "count": 2,
  "offer_market_facet": {
    "buckets": []
  },
  "offer_depot_facet": {
    "buckets": []
  }
}
```

## Document Structure

The Solr index has a nested document structure:

1. **Parent Documents (Products)**:
   - Fields: `id`, `title`, `brand`, `categories`, etc.
   - Example: `{"id":"0000000000LRI", "title":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr", ...}`

2. **Child Documents (Offers/Depots)**:
   - Stored in a `depots` array within parent documents
   - Fields: `offer_id`, `offer_price`, `offer_market`, `offer_depot`, etc.
   - Special fields: `_nest_path_` (like "/depots#0"), `_nest_parent_` (points to parent ID)
   - Example: `{"id":"hakmar-1015466_5816", "offer_market":"hakmar", "_nest_path_":"/depots#0", "_nest_parent_":"0000000000LRI", ...}`

## Attempted Solutions

Several approaches were attempted to solve this problem:

1. **Using blockChildren Domain**:
   ```json
   "domain": {
     "blockChildren": "_nest_path_:/depots*"
   }
   ```
   This approach returned empty buckets.

2. **Using Domain with Filter**:
   ```json
   "domain": {
     "filter": "_nest_path_:*"
   }
   ```
   This also returned empty buckets when querying parent documents.

3. **Using Join Domain**:
   ```json
   "domain": {
     "join": {
       "from": "_nest_parent_",
       "to": "id"
     }
   }
   ```
   This approach also returned empty buckets.

4. **Using Dot Notation**:
   ```json
   "field": "depots.offer_market"
   ```
   This failed with an "undefined field" error.

## The Working Solution But it is not feasible because it requires two queries and for large number of parent documents, it is not performant. So you can use this approach to test and understand the problem but not solution.

The most reliable approach for faceting on child documents in Solr 9.6 is to use a two-step process:

1. **First Query**: Get parent document IDs
   ```
   http://10.8.133.89:8981/solr/dev_market/select?q=title:Cino&rows=100&fl=id
   ```

2. **Second Query**: Query child documents directly with a filter on `_nest_parent_`
   ```
   http://10.8.133.89:8981/solr/dev_market/select?q=_nest_path_:*&fq=_nest_parent_:(0000000000LRI OR 0000000000KL2)&rows=0&json.facet={"offer_market_facet":{"type":"terms","field":"offer_market","limit":10}}
   ```

This approach consistently returns accurate facet counts for child document fields:
```json
"facets": {
  "count": 14901,
  "offer_market_facet": {
    "buckets": [{
      "val": "a101",
      "count": 12654
    }, {
      "val": "migros",
      "count": 1478
    }, {
      "val": "hakmar",
      "count": 769
    }]
  }
}
```

## Additional Non-Working Strategies Discovered Through Research

### 5. **Using blockChildren Domain with Basic Configuration**:
   ```json
   "domain": {
     "blockChildren": "_nest_path_:/depots*"
   }
   ```
   This approach was documented in problem.md as returning empty buckets, likely due to incorrect parent filter specification.

### 6. **Using Join Domain with Incorrect Direction**:
   ```json
   "domain": {
     "filter": [
       "{!join from=id to=_nest_parent_}($q)",
       "$childFilter"
     ]
   }
   ```
   This approach tries to join parent IDs to child `_nest_parent_` field but doesn't properly handle contextual filtering in parent-child scenarios.

### 7. **Parameter Substitution Without Proper Context**:
   The current implementation uses parameter substitution but doesn't properly set up the context for parent-child relationships, leading to empty or incorrect facet results.

## Research Findings: Correct Approach for Multi-Select Faceting

After researching Grid Dynamics' detailed article on multi-select faceting for nested documents in Solr, the correct approach is more complex than initially thought:

### **Issue with Simple blockChildren Domain**:
The simple `blockChildren` approach doesn't work for contextual filtering because:
1. It doesn't properly handle filter exclusions for multi-select faceting
2. It doesn't apply the current query filters to the facet domain
3. It doesn't support parameter substitution for dynamic filtering

### **Correct Approach for Multi-Select Faceting**:
Based on Grid Dynamics research (SOLR-8998 and SOLR-9510), the proper solution requires:

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "excludeTags": ["top"],
      "filter": [
        "{!filters param=$child.fq excludeTags=color v=$childquery}",
        "{!child of=scope:product filters=$fq}scope:product"
      ]
    },
    "facet": {
      "product_count": "uniqueBlock(_root_)"
    },
    "excludeTags": ["OFFER_MARKET_FILTER_TAG"],
    "limit": -1,
    "mincount": 1
  }
}
```

### **Key Requirements**:
1. **Tagged Filters**: All filters must be tagged for exclusion
2. **Complex Domain**: Domain must reconstruct the filter set excluding specific tags
3. **Parent-Child Coordination**: Must coordinate between parent and child filters
4. **Performance Optimization**: Use `limit: -1` for better performance with `uniqueBlock`

## Current Implementation Status (After Refactoring)

### **Implemented Solution**:
Based on Yonik's documentation on facet domains, we implemented:

```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockChildren": "$parentFilter"
    },
    "facet": {
      "product_count": "uniqueBlock(_root_)"
    },
    "excludeTags": ["OFFER_MARKET_FILTER_TAG"],
    "limit": 100,
    "mincount": 1
  }
}
```

### **Implementation Benefits**:
1. **Centralized Filter Management**: All filter parameters defined in one place
2. **Parameter Substitution**: Consistent use of $parentFilter across query components
3. **Simplified Approach**: Uses standard blockChildren domain without complex filter reconstruction
4. **Multi-Select Support**: Proper excludeTags implementation

### **Potential Issues to Monitor**:
1. **Parameter Substitution in Domain**: Need to verify Solr properly substitutes parameters in blockChildren domain
2. **Contextual Filtering**: Need to test that facets actually respect applied filters
3. **Performance**: Monitor if this approach performs well with large datasets

### **Testing Required**:
- Verify facets are contextually filtered when depot filters are applied
- Test multi-select faceting behavior with excludeTags
- Performance testing with realistic data volumes

## Final Implementation: Enhanced Contextual Filtering

### **Problem Solved**:
The original implementation had `$parentFilter` set to only `"*:* -_nest_path_:*"` which didn't include user-applied filters, resulting in non-contextual facets.

### **Solution Implemented**:
1. **Two-Phase Filter Setup**:
   - `setupBasicParentChildFilterParameters()`: Sets basic structure before user filters
   - `setupContextualFilterParameters()`: Builds contextual parent filter after user filters

2. **Enhanced Contextual Parent Filter**:
   ```
   Basic: "*:* -_nest_path_:*"
   Enhanced: "*:* -_nest_path_:* AND (main_category:\"Çikolata\") AND (brand:\"Ülker\")"
   ```

3. **Smart Filter Inclusion**:
   - Includes parent document filters (category, brand, etc.)
   - Excludes child document filters (market, depot, price)
   - Respects excludeTags for multi-select faceting

### **Verification Results**:
✅ **Contextual Filtering**: Parent filters now include user-applied filters
✅ **ExcludeTags Support**: Tagged filters properly excluded from facet domains
✅ **Filter Consistency**: Same logic used across query, facets, and projection
✅ **Query Complexity**: Manageable for typical use cases, with monitoring recommendations

### **Expected Behavior**:
- **Category Filter Applied**: Market facets only show markets with products in that category
- **Multiple Filters**: Facets respect all applied filters for true contextual filtering
- **Multi-Select**: Users can select multiple values while seeing all options (via excludeTags)
- **Performance**: Moderate complexity for typical scenarios, high complexity monitoring available

### **Performance Considerations**:
- Filter length typically <200 characters for normal use
- Query complexity increases with multiple filters
- Recommend monitoring for >5 simultaneous filters
- Consider caching strategies for high-traffic scenarios

## Here is the sample response from of example doc.

For more detailed information, sub-facets can be used to see the distribution of markets by parent document:
```json
"response":{
    "numFound":1000,
    "start":0,
    "numFoundExact":true,
    "docs":[{
      "id":"0000000000LRI",
      "barcodes":["8680913482227","8680913481114","8682442231994"],
      "title":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "title_spellcheck":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "title_zem":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "brand":"Cino",
      "refined_volume_weight":"25.0 GR",
      "main_category":"Çikolata",
      "sub_category":["Çikolatalar"],
      "index_time":"07.05.2025 19:38",
      "categories":["Bar Ve Kaplamalar","Bisküvi Ve Çikolata","Bisküvi,şekerleme,mamalar Ve Sağlık Ürün","Bar Ve Ka","Çikolata Kaplamalılar","Çikolata Kaplamalı Barlar","Çi̇kolata Kaplamalı Barlar"],
      "image_url":"https://cdn.cimri.io/market/500x500/-_437774.jpg",
      "offer_discount":false,
      "_version_":1831480783738503168,
      "depots":[{
        "id":"hakmar-1015466_5816",
        "offer_id":"hakmar-1015466_5816",
        "offer_price":6.0,
        "offer_market":"hakmar",
        "offer_depot":"hakmar-5816",
        "offer_update_date":"2025-05-07T19:38:37.691Z",
        "_nest_path_":"/depots#0",
        "_nest_parent_":"0000000000LRI",
        "offer_discount":false,
        "_version_":1831480783738503168
      }]
    }]
  },
  ```

Solr configuration is inside ```/Users/<USER>/developer/springws/market-indexer/solr-config/compose-solr-child/``` but you can also use solr api to check the configuration. the solr is in cloud mode. 


## Implementation Considerations

In a real application, this two-step process would be implemented as:
1. Make the first query to get parent IDs
2. Extract the IDs from the response
3. Build a second query with those IDs to get facets
4. Present both the parent documents and facets to the user

This approach uses standard Solr features and consistently provides accurate facet counts for child documents.
