# Understanding the Solr Nested Document Faceting Problem

## The Problem

The initial problem was related to faceting on child documents in Solr 9.6 when using nested document structures. Specifically, when trying to get facet counts for fields in child documents (like `offer_market` and `offer_depot`) while querying parent documents, the facet results were empty or incorrect.

The query was structured like this:
```
http://10.8.133.89:8981/solr/dev_market/select?q=title:Cino&rows=0&json.facet={"offer_market_facet":{"type":"terms","field":"offer_market","domain":{"blockChildren":"_nest_path_:/depots*"},"facet":{"parents":"unique(_root_)"},"excludeTags":["OFFER_MARKET_FILTER_TAG"],"limit":100},"offer_depot_facet":{"type":"terms","field":"offer_depot","domain":{"blockChildren":"_nest_path_:/depots*"},"facet":{"parents":"unique(_root_)"},"excludeTags":["OFFER_DEPOT_FILTER_TAG"],"limit":100}}

```
this is from one of my old project maybe we should go in this direction: sample code
```
  private void addFacetForReturnedArtifactCount(final SolrQuery solrFacetQuery) {
        solrFacetQuery.set("json.facet", "{ \"counts\": { \"type\": \"terms\",      \"field\": \"ulkeAd\",       \"facet\":{ artifactCount: 'sum(returnedArtifactCount)'} } } ");
    }
```


With a JSON facet configuration like:
```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockChildren": "_nest_path_:/depots*"
    },
    "facet": {
      "parents": "unique(_root_)"
    },
    "excludeTags": ["OFFER_MARKET_FILTER_TAG"],
    "limit": 100
  },
  "offer_depot_facet": {
    "type": "terms",
    "field": "offer_depot",
    "domain": {
      "blockChildren": "_nest_path_:/depots*"
    },
    "facet": {
      "parents": "unique(_root_)"
    },
    "excludeTags": ["OFFER_DEPOT_FILTER_TAG"],
    "limit": 100
  }
}
```

However, this configuration was not working, and the facet results were empty:
```json
"facets": {
  "count": 2,
  "offer_market_facet": {
    "buckets": []
  },
  "offer_depot_facet": {
    "buckets": []
  }
}
```

## Document Structure

The Solr index has a nested document structure:

1. **Parent Documents (Products)**:
   - Fields: `id`, `title`, `brand`, `categories`, etc.
   - Example: `{"id":"0000000000LRI", "title":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr", ...}`

2. **Child Documents (Offers/Depots)**:
   - Stored in a `depots` array within parent documents
   - Fields: `offer_id`, `offer_price`, `offer_market`, `offer_depot`, etc.
   - Special fields: `_nest_path_` (like "/depots#0"), `_nest_parent_` (points to parent ID)
   - Example: `{"id":"hakmar-1015466_5816", "offer_market":"hakmar", "_nest_path_":"/depots#0", "_nest_parent_":"0000000000LRI", ...}`

## Attempted Solutions

Several approaches were attempted to solve this problem:

1. **Using blockChildren Domain**:
   ```json
   "domain": {
     "blockChildren": "_nest_path_:/depots*"
   }
   ```
   This approach returned empty buckets.

2. **Using Domain with Filter**:
   ```json
   "domain": {
     "filter": "_nest_path_:*"
   }
   ```
   This also returned empty buckets when querying parent documents.

3. **Using Join Domain**:
   ```json
   "domain": {
     "join": {
       "from": "_nest_parent_",
       "to": "id"
     }
   }
   ```
   This approach also returned empty buckets.

4. **Using Dot Notation**:
   ```json
   "field": "depots.offer_market"
   ```
   This failed with an "undefined field" error.

## The Working Solution But it is not feasible because it requires two queries and for large number of parent documents, it is not performant. So you can use this approach to test and understand the problem but not solution.

The most reliable approach for faceting on child documents in Solr 9.6 is to use a two-step process:

1. **First Query**: Get parent document IDs
   ```
   http://10.8.133.89:8981/solr/dev_market/select?q=title:Cino&rows=100&fl=id
   ```

2. **Second Query**: Query child documents directly with a filter on `_nest_parent_`
   ```
   http://10.8.133.89:8981/solr/dev_market/select?q=_nest_path_:*&fq=_nest_parent_:(0000000000LRI OR 0000000000KL2)&rows=0&json.facet={"offer_market_facet":{"type":"terms","field":"offer_market","limit":10}}
   ```

This approach consistently returns accurate facet counts for child document fields:
```json
"facets": {
  "count": 14901,
  "offer_market_facet": {
    "buckets": [{
      "val": "a101",
      "count": 12654
    }, {
      "val": "migros",
      "count": 1478
    }, {
      "val": "hakmar",
      "count": 769
    }]
  }
}
```

## Additional Non-Working Strategies Discovered Through Research

### 5. **Using blockChildren Domain with Basic Configuration**:
   ```json
   "domain": {
     "blockChildren": "_nest_path_:/depots*"
   }
   ```
   This approach was documented in problem.md as returning empty buckets, likely due to incorrect parent filter specification.

### 6. **Using Join Domain with Incorrect Direction**:
   ```json
   "domain": {
     "filter": [
       "{!join from=id to=_nest_parent_}($q)",
       "$childFilter"
     ]
   }
   ```
   This approach tries to join parent IDs to child `_nest_parent_` field but doesn't properly handle contextual filtering in parent-child scenarios.

### 7. **Parameter Substitution Without Proper Context**:
   The current implementation uses parameter substitution but doesn't properly set up the context for parent-child relationships, leading to empty or incorrect facet results.

## Working Solution Based on Solr 9.6+ Best Practices

After researching the latest Solr documentation, the recommended approach for parent-child faceting is:

### **blockChildren Domain with Proper Parent Filter**:
```json
{
  "offer_market_facet": {
    "type": "terms",
    "field": "offer_market",
    "domain": {
      "blockChildren": "*:* -_nest_path_:*"
    },
    "facet": {
      "product_count": "uniqueBlock(_root_)"
    },
    "limit": 100,
    "mincount": 1
  }
}
```

This approach:
1. Uses `blockChildren` with the correct parent filter `*:* -_nest_path_:*`
2. Applies the main query and filters automatically to parent documents
3. Transforms the domain to include only child documents of matching parents
4. Uses `uniqueBlock(_root_)` to count unique parent documents per facet bucket

## Here is the sample response from of example doc.

For more detailed information, sub-facets can be used to see the distribution of markets by parent document:
```json
"response":{
    "numFound":1000,
    "start":0,
    "numFoundExact":true,
    "docs":[{
      "id":"0000000000LRI",
      "barcodes":["8680913482227","8680913481114","8682442231994"],
      "title":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "title_spellcheck":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "title_zem":"Cino Sütlü Çikolata kaplı Gerçek Kayısı Parçalı Bar 25 gr",
      "brand":"Cino",
      "refined_volume_weight":"25.0 GR",
      "main_category":"Çikolata",
      "sub_category":["Çikolatalar"],
      "index_time":"07.05.2025 19:38",
      "categories":["Bar Ve Kaplamalar","Bisküvi Ve Çikolata","Bisküvi,şekerleme,mamalar Ve Sağlık Ürün","Bar Ve Ka","Çikolata Kaplamalılar","Çikolata Kaplamalı Barlar","Çi̇kolata Kaplamalı Barlar"],
      "image_url":"https://cdn.cimri.io/market/500x500/-_437774.jpg",
      "offer_discount":false,
      "_version_":1831480783738503168,
      "depots":[{
        "id":"hakmar-1015466_5816",
        "offer_id":"hakmar-1015466_5816",
        "offer_price":6.0,
        "offer_market":"hakmar",
        "offer_depot":"hakmar-5816",
        "offer_update_date":"2025-05-07T19:38:37.691Z",
        "_nest_path_":"/depots#0",
        "_nest_parent_":"0000000000LRI",
        "offer_discount":false,
        "_version_":1831480783738503168
      }]
    }]
  },
  ```

Solr configuration is inside ```/Users/<USER>/developer/springws/market-indexer/solr-config/compose-solr-child/``` but you can also use solr api to check the configuration. the solr is in cloud mode. 


## Implementation Considerations

In a real application, this two-step process would be implemented as:
1. Make the first query to get parent IDs
2. Extract the IDs from the response
3. Build a second query with those IDs to get facets
4. Present both the parent documents and facets to the user

This approach uses standard Solr features and consistently provides accurate facet counts for child documents.
